<template>
  <div v-if="results" class="space-y-1 relative">
    <!-- 标签页导航 -->
    <div class="tabs tabs-box bg-base-100 border">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        :class="`tab tab-lg ${activeTab === tab.id ? 'tab-active' : ''}`"
        @click="activeTab = tab.id"
      >
        <span class="mr-2">{{ tab.icon }}</span>
        {{ tab.name }}
      </button>
    </div>

    <!-- 标签页内容 -->
    <div v-if="activeTab === 'stats'">
      <!-- 性能统计 -->
      <PerformanceStats :stats="results.performance_stats" :trade-records="results.trade_records" />
    </div>

    <DailyPortfolioView
      v-if="activeTab === 'portfolio'"
      :portfolio-history="results.portfolio_history"
    />

    <TradeRecords v-if="activeTab === 'trades'" :trades="results.trade_records" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import PerformanceStats from './PerformanceStats.vue'
import DailyPortfolioView from './DailyPortfolioView.vue'
import TradeRecords from './TradeRecords.vue'

const props = defineProps({
  results: {
    type: Object,
    default: null,
  },
})

const activeTab = ref('stats')

const tabs = [
  { id: 'stats', name: '回测统计指标', icon: '📊' },
  { id: 'portfolio', name: '每日资产与持仓', icon: '💰' },
  { id: 'trades', name: '成交记录', icon: '📋' },
]
</script>
