from pathlib import Path

import yaml

from quantback.utils import SingletonMeta


class Config(metaclass=SingletonMeta):
    def __init__(self):
        self._config = {}
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        # 获取配置文件的绝对路径
        config_path = Path(__file__).parent / 'config.yaml'

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
        except FileNotFoundError:
            print(f'配置文件不存在: {config_path}')
        except yaml.YAMLError as e:
            print(f'配置文件格式错误: {e}')

    def get(self, key: str, default=None):
        """
        获取配置值
        :param key: 配置键，支持点号分隔的多级键，如 'dolphindb.host'
        :param default: 默认值，当键不存在时返回
        :return: 配置值
        """

        keys = key.split('.')
        value = self._config

        for k in keys:
            if isinstance(value, dict):
                value = value.get(k)
                if value is None:
                    return default
            else:
                return default

        return value

    def set(self, key: str, value):
        """
        设置配置值, 只用于在内存中临时修改配置，不会影响到config.yaml文件
        :param key: 配置键，支持点号分隔的多级键，如 'duckdb.path'
        :param value: 要设置的值
        :return: None
        """
        keys = key.split('.')
        config = self._config

        # 遍历除最后一个键以外的所有键
        for k in keys[:-1]:
            # 如果键不存在于当前层次的字典中，则创建一个新的空字典
            if k not in config:
                config[k] = {}
            # 如果当前值不是字典，将其替换为空字典
            elif not isinstance(config[k], dict):
                config[k] = {}
            # 移动到下一层
            config = config[k]

        # 设置最后一个键的值
        config[keys[-1]] = value


def get_config():
    return Config()
