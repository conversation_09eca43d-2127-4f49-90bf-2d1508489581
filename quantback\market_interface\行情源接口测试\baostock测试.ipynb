{"cells": [{"cell_type": "code", "execution_count": 16, "id": "e566e03b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["login success!\n", "login respond error_code:0\n", "login respond  error_msg:success\n"]}], "source": ["import baostock as bs\n", "import pandas as pd\n", "\n", "#### 登陆系统 ####\n", "lg = bs.login()\n", "# 显示登陆返回信息\n", "print('login respond error_code:' + lg.error_code)\n", "print('login respond  error_msg:' + lg.error_msg)"]}, {"cell_type": "code", "execution_count": 22, "id": "701efd74", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["query_history_k_data_plus respond error_code:0\n", "query_history_k_data_plus respond  error_msg:success\n", "            date       code     open     high      low    close preclose  \\\n", "0     2012-06-05  sz.002680  31.1000  33.9800  30.5000  31.9000  21.5900   \n", "1     2012-06-06  sz.002680  32.0000  34.7700  31.0500  32.9500  31.9000   \n", "2     2012-06-07  sz.002680  32.8600  33.7700  32.4000  33.6600  32.9500   \n", "3     2012-06-08  sz.002680  33.8000  34.8300  32.9800  33.0000  33.6600   \n", "4     2012-06-11  sz.002680  32.7000  33.7400  32.6000  32.8900  33.0000   \n", "...          ...        ...      ...      ...      ...      ...      ...   \n", "1817  2019-11-21  sz.002680   0.7200   0.7300   0.6900   0.7300   0.6600   \n", "1818  2019-11-22  sz.002680   0.7700   0.8000   0.6700   0.7000   0.7300   \n", "1819  2019-11-25  sz.002680   0.7100   0.7400   0.6800   0.7100   0.7000   \n", "1820  2019-11-26  sz.002680   0.7100   0.7800   0.7100   0.7700   0.7100   \n", "1821  2019-11-27  sz.002680   0.7700   0.7700   0.7700   0.7700   0.7700   \n", "\n", "        volume          amount adjustflag       turn tradestatus     pctChg  \\\n", "0     16212985  516920715.9000          3  81.064925           1  47.753590   \n", "1     11618934  378626042.8100          3  58.094670           1   3.291500   \n", "2      7096500  234470630.7900          3  35.482500           1   2.154800   \n", "3      5359831  182179940.2900          3  26.799155           1  -1.960800   \n", "4      4513464  149513673.2800          3  22.567320           1  -0.333300   \n", "...        ...             ...        ...        ...         ...        ...   \n", "1817  14055601   10107424.0000          3   2.420000           1  10.606100   \n", "1818  17126608   12813624.0000          3   2.948700           1  -4.109600   \n", "1819  11688308    8278600.0800          3   2.012400           1   1.428600   \n", "1820  15774340   11879019.5200          3   2.715900           1   8.450700   \n", "1821                                    3                      1              \n", "\n", "     isST  \n", "0       0  \n", "1       0  \n", "2       0  \n", "3       0  \n", "4       0  \n", "...   ...  \n", "1817    0  \n", "1818    0  \n", "1819    0  \n", "1820    0  \n", "1821    0  \n", "\n", "[1822 rows x 14 columns]\n"]}], "source": ["#### 获取沪深A股历史K线数据 ####\n", "# 详细指标参数，参见“历史行情指标参数”章节；“分钟线”参数与“日线”参数不同。“分钟线”不包含指数。\n", "# 分钟线指标：date,time,code,open,high,low,close,volume,amount,adjustflag\n", "# 周月线指标：date,code,open,high,low,close,volume,amount,adjustflag,turn,pctChg\n", "rs = bs.query_history_k_data_plus(\n", "    \"sz.002680\",\n", "    \"date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST\",\n", "    start_date='1990-01-01',\n", "    end_date='',\n", "    frequency=\"d\",\n", "    adjustflag=\"3\",\n", ")\n", "print('query_history_k_data_plus respond error_code:' + rs.error_code)\n", "print('query_history_k_data_plus respond  error_msg:' + rs.error_msg)\n", "\n", "#### 打印结果集 ####\n", "data_list = []\n", "while (rs.error_code == '0') & rs.next():\n", "    # 获取一条记录，将记录合并在一起\n", "    data_list.append(rs.get_row_data())\n", "result = pd.DataFrame(data_list, columns=rs.fields)\n", "\n", "#### 结果集输出到csv文件 ####\n", "print(result)\n", "\n", "#### 登出系统 ####\n", "# bs.logout()"]}, {"cell_type": "code", "execution_count": 12, "id": "19a16c62", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["query_history_k_data_plus respond error_code:0\n", "query_history_k_data_plus respond  error_msg:success\n", "             date               time       code    open    high     low  \\\n", "0      2019-01-02  20190102093500000  sz.000006  5.1800  5.2100  5.1800   \n", "1      2019-01-02  20190102094000000  sz.000006  5.2100  5.2400  5.2000   \n", "2      2019-01-02  20190102094500000  sz.000006  5.2100  5.2500  5.1800   \n", "3      2019-01-02  20190102095000000  sz.000006  5.2500  5.2500  5.1900   \n", "4      2019-01-02  20190102095500000  sz.000006  5.2200  5.2200  5.1800   \n", "...           ...                ...        ...     ...     ...     ...   \n", "74731  2025-06-06  20250606144000000  sz.000006  6.7700  6.7900  6.7700   \n", "74732  2025-06-06  20250606144500000  sz.000006  6.7700  6.7800  6.7600   \n", "74733  2025-06-06  20250606145000000  sz.000006  6.7600  6.7800  6.7600   \n", "74734  2025-06-06  20250606145500000  sz.000006  6.7800  6.7900  6.7700   \n", "74735  2025-06-06  20250606150000000  sz.000006  6.7800  6.8100  6.7800   \n", "\n", "        close  volume        amount adjustflag  \n", "0      5.2100  157600   818394.0000          3  \n", "1      5.2100  408300  2128769.0000          3  \n", "2      5.2500  407791  2125899.0000          3  \n", "3      5.2200  237204  1236473.0000          3  \n", "4      5.1800  279396  1452499.0000          3  \n", "...       ...     ...           ...        ...  \n", "74731  6.7700  294200  1993808.0000          3  \n", "74732  6.7700  522800  3536008.0000          3  \n", "74733  6.7800  331300  2242672.0000          3  \n", "74734  6.7800  764500  5184544.0000          3  \n", "74735  6.8000  996600  6773936.0000          3  \n", "\n", "[74736 rows x 10 columns]\n"]}], "source": ["#### 获取沪深A股历史K线数据 ####\n", "# 详细指标参数，参见“历史行情指标参数”章节；“分钟线”参数与“日线”参数不同。“分钟线”不包含指数。\n", "# 分钟线指标：date,time,code,open,high,low,close,volume,amount,adjustflag\n", "# 周月线指标：date,code,open,high,low,close,volume,amount,adjustflag,turn,pctChg\n", "rs = bs.query_history_k_data_plus(\n", "    \"sz.000006\",\n", "    \"date,time,code,open,high,low,close,volume,amount,adjustflag\",\n", "    start_date='1990-01-01',\n", "    end_date='',\n", "    frequency=\"5\",\n", "    adjustflag=\"3\",\n", ")\n", "print('query_history_k_data_plus respond error_code:' + rs.error_code)\n", "print('query_history_k_data_plus respond  error_msg:' + rs.error_msg)\n", "\n", "#### 打印结果集 ####\n", "data_list = []\n", "while (rs.error_code == '0') & rs.next():\n", "    # 获取一条记录，将记录合并在一起\n", "    data_list.append(rs.get_row_data())\n", "result = pd.DataFrame(data_list, columns=rs.fields)\n", "\n", "#### 结果集输出到csv文件 ####\n", "# result.to_csv(\"D:\\\\history_A_stock_k_data.csv\", index=False)\n", "print(result)\n", "\n", "#### 登出系统 ####\n", "# bs.logout()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}