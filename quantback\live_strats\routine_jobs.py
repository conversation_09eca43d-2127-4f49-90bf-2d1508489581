import logging

from xtquant import xtconstant, xtdata

from quantback.market_interface.trade_service import TradeService

logger = logging.getLogger(__name__)


def order_reverse_repo():
    """
    国债逆回购下单
    """
    tradeService = TradeService()
    xt_trader = tradeService.xt_trader
    stock_account = tradeService.stock_account
    ticks = xtdata.get_full_tick(["131810.SZ", "204001.SH"])
    sz = ticks["131810.SZ"]
    sh = ticks["204001.SH"]
    if sz["lastPrice"] > sh["lastPrice"]:
        stock_code = "131810.SZ"
        price = sz["bidPrice"][2]  # 买3价格
    else:
        stock_code = "204001.SH"
        price = sh["bidPrice"][2]  # 买3价格

    asset = xt_trader.query_stock_asset(stock_account)
    available_cash = asset.cash
    volume = int(available_cash / 1000) * 10  # 国债逆回购至少1000元,对应数量10张
    seq = xt_trader.order_stock_async(
        stock_account,
        stock_code,
        xtconstant.STOCK_SELL,
        volume,
        xtconstant.FIX_PRICE,  # 国债逆回购应该是不支持市价下单
        price,
        "",
        "",
    )
    logger.info(f"国债逆回购下单seq: {seq}")
