<template>
  <div class="card bg-base-100 border">
    <div class="card-body">
      <div v-if="!stats">
        <h2 class="card-title">回测统计</h2>
        <p class="text-gray-500">暂无统计数据</p>
      </div>
      <div v-else>
        <h2 class="card-title">回测统计指标</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          <!-- 收益指标 -->
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">总收益率</div>
            <div :class="`stat-value text-2xl ${getReturnColor(stats.total_return)}`">
              {{ formatPercent(stats.total_return) }}
            </div>
            <div class="stat-desc">整个回测期间的总收益</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">年化收益率</div>
            <div :class="`stat-value text-2xl ${getReturnColor(stats.annual_return)}`">
              {{ formatPercent(stats.annual_return) }}
            </div>
            <div class="stat-desc">年化后的收益率</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">最大回撤</div>
            <div class="stat-value text-2xl text-error">
              {{ formatPercent(stats.max_drawdown) }}
            </div>
            <div class="stat-desc">最大的资产回撤幅度</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">夏普比率</div>
            <div :class="`stat-value text-2xl ${getSharpeColor(stats.sharpe_ratio)}`">
              {{ formatNumber(stats.sharpe_ratio) }}
            </div>
            <div class="stat-desc">风险调整后收益</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">卡尔马比率</div>
            <div :class="`stat-value text-2xl ${getSharpeColor(stats.calmar_ratio)}`">
              {{ formatNumber(stats.calmar_ratio) }}
            </div>
            <div class="stat-desc">年化收益/最大回撤</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">索提诺比率</div>
            <div :class="`stat-value text-2xl ${getSharpeColor(stats.sortino_ratio)}`">
              {{ formatNumber(stats.sortino_ratio) }}
            </div>
            <div class="stat-desc">下行风险调整收益</div>
          </div>

          <!-- 风险指标 -->
          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">年化波动率</div>
            <div class="stat-value text-2xl">
              {{ formatPercent(stats.annual_volatility) }}
            </div>
            <div class="stat-desc">收益率的年化标准差</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">95% VaR</div>
            <div class="stat-value text-2xl text-warning">
              {{ formatPercent(stats.var_95) }}
            </div>
            <div class="stat-desc">95%置信度风险价值</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">胜率</div>
            <div class="stat-value text-2xl">
              {{ formatPercent(stats.win_rate) }}
            </div>
            <div class="stat-desc">盈利交易日占比</div>
          </div>

          <div class="stat bg-base-200 rounded-lg">
            <div class="stat-title">盈亏比</div>
            <div class="stat-value text-2xl">
              {{ formatNumber(profitLossRatio) }}
            </div>
            <div class="stat-desc">平均盈利/平均亏损</div>
          </div>
        </div>

        <!-- 额外信息 -->
        <div v-if="hasExtraInfo" class="divider">其他信息</div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
          <div v-if="actualTotalTrades > 0" class="stat bg-base-200 rounded-lg">
            <div class="stat-title">总交易次数</div>
            <div class="stat-value text-xl">
              {{ actualTotalTrades }}
            </div>
          </div>

          <div
            v-if="stats.benchmark_total_return !== undefined"
            class="stat bg-base-200 rounded-lg"
          >
            <div class="stat-title">基准收益率</div>
            <div :class="`stat-value text-xl ${getReturnColor(stats.benchmark_total_return)}`">
              {{ formatPercent(stats.benchmark_total_return) }}
            </div>
          </div>

          <div v-if="stats.excess_return !== undefined" class="stat bg-base-200 rounded-lg">
            <div class="stat-title">超额收益</div>
            <div :class="`stat-value text-xl ${getReturnColor(stats.excess_return)}`">
              {{ formatPercent(stats.excess_return) }}
            </div>
          </div>

          <div v-if="stats.alpha !== undefined" class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Alpha</div>
            <div :class="`stat-value text-xl ${getReturnColor(stats.alpha)}`">
              {{ formatPercent(stats.alpha) }}
            </div>
          </div>

          <div v-if="stats.beta !== undefined" class="stat bg-base-200 rounded-lg">
            <div class="stat-title">Beta系数</div>
            <div :class="`stat-value text-xl ${getBetaColor(stats.beta)}`">
              {{ formatNumber(stats.beta) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  stats: {
    type: Object,
    default: null,
  },
  tradeRecords: {
    type: Array,
    default: () => [],
  },
})

const actualTotalTrades = computed(() => {
  return props.tradeRecords ? props.tradeRecords.length : props.stats?.total_trades || 0
})

const profitLossRatio = computed(() => {
  if (!props.stats || !props.stats.avg_win || !props.stats.avg_loss) return null
  // 盈亏比 = 平均盈利 / |平均亏损|
  return props.stats.avg_win / Math.abs(props.stats.avg_loss)
})

const hasExtraInfo = computed(() => {
  return (
    props.stats &&
    (actualTotalTrades.value > 0 ||
      props.stats.benchmark_total_return !== undefined ||
      props.stats.excess_return !== undefined ||
      props.stats.beta !== undefined ||
      props.stats.alpha !== undefined)
  )
})

const formatPercent = (value) => {
  if (value === null || value === undefined || isNaN(value)) return 'N/A'
  return `${(value * 100).toFixed(2)}%`
}

const formatNumber = (value, decimals = 2) => {
  if (value === null || value === undefined || isNaN(value)) return 'N/A'
  return value.toFixed(decimals)
}

const formatCurrency = (value) => {
  if (value === null || value === undefined || isNaN(value)) return 'N/A'
  return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

const getReturnColor = (value) => {
  if (value === null || value === undefined || isNaN(value)) return 'text-gray-500'
  return value >= 0 ? 'text-success' : 'text-error'
}

const getSharpeColor = (value) => {
  if (value === null || value === undefined || isNaN(value)) return 'text-gray-500'
  if (value >= 1) return 'text-success'
  if (value >= 0) return 'text-warning'
  return 'text-error'
}

const getBetaColor = (value) => {
  if (value === null || value === undefined || isNaN(value)) return 'text-gray-500'
  if (value >= 0.8 && value <= 1.2) return 'text-success' // 接近市场风险
  if (value >= 0.5 && value <= 1.5) return 'text-warning' // 中等风险
  return 'text-error' // 极端风险
}
</script>
