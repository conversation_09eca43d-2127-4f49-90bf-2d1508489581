from functools import lru_cache

from pypinyin import Style, pinyin


def identity(arg):
    return arg


@lru_cache(maxsize=None)  # maxsize=None 表示无限条项目
def to_pinyin_abbr(text: str) -> str:
    """
    将中文文本转换为拼音首字母缩写（带缓存）

    Args:
        text: 输入的中文文本

    Returns:
        str: 拼音首字母缩写（大写）

    Examples:
        >>> to_pinyin_abbr("趋势追踪")  # 第一次调用会计算
        'QSZZ'
        >>> to_pinyin_abbr("趋势追踪")  # 第二次调用直接从缓存返回
        'QSZZ'
    """
    if not text:
        return ""

    # 获取每个字的拼音首字母, 非中文字符直接返回
    abbr = [
        pinyin(char, style=Style.FIRST_LETTER)[0][0] if '\u4e00' <= char <= '\u9fff' else char
        for char in text.strip().replace(" ", "")[:20]
    ]
    return ''.join(abbr).upper()
