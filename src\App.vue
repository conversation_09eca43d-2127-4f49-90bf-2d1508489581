<template>
  <SidebarProvider>
    <AppSideBar />
    <SidebarInset>
      <main>
        <div class="min-h-screen bg-base-200">
          <div class="navbar bg-base-200">
            <div class="flex-1"><SidebarTrigger /></div>
            <div class="flex-none">
              <div
                :class="`badge ${connected ? 'badge-success' : 'badge-error'} flex items-center gap-1`"
              >
                <Wifi v-if="connected" :size="16" />
                <WifiOff v-else :size="16" />
                {{ connected ? '已连接' : '未连接' }}
              </div>
            </div>
          </div>
          <div v-if="initialized">
            <router-view />
          </div>
          <div v-else class="container mx-auto p-4">
            <div class="flex flex-col items-center justify-center min-h-[200px] gap-4">
              <span class="loading loading-spinner loading-lg text-primary"></span>
              <div class="text-lg font-medium text-gray-600">正在连接到服务器...</div>
            </div>
          </div>
        </div>
      </main>
    </SidebarInset>
  </SidebarProvider>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Wifi, WifiOff } from 'lucide-vue-next'
import { socket } from './utils/socket'
import { SidebarProvider, SidebarTrigger, SidebarInset } from './components/ui/sidebar'
import AppSideBar from './components/AppSideBar.vue'

const initialized = ref(false)
const connected = ref(socket.connected)

function onConnect() {
  connected.value = true
  initialized.value = true
  console.log('Socket.IO已连接')
}

function onDisconnect() {
  connected.value = false
  console.log('Socket.IO连接断开')
}

onMounted(() => {
  // 注册事件监听器
  socket.on('connect', onConnect)
  socket.on('disconnect', onDisconnect)
})

onUnmounted(() => {
  socket.off('connect', onConnect)
  socket.off('disconnect', onDisconnect)
})
</script>
