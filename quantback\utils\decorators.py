import logging
import time
from functools import wraps


def advanced_func_stats(func):
    """统计一个函数的执行时间、调用次数、平均执行时间等信息的装饰器"""
    call_count = 0
    total_time = 0.0
    # 使用函数所在模块的logger
    logger = logging.getLogger(func.__module__)

    @wraps(func)
    def wrapper(*args, **kwargs):
        nonlocal call_count, total_time
        call_count += 1

        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()

        duration = end_time - start_time
        total_time += duration
        average_time = total_time / call_count

        logger.debug(
            f'[STATS] 函数 {func.__name__} 第 {call_count} 次调用, 耗时: {duration:.4f} 秒'
        )
        logger.debug(f'[STATS] 累计耗时: {total_time:.4f} 秒, 平均每次耗时: {average_time:.4f} 秒')

        return result

    return wrapper
