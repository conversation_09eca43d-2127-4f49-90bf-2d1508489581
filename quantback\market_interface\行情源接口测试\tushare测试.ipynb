{"cells": [{"cell_type": "code", "execution_count": 1, "id": "928ef1f2", "metadata": {}, "outputs": [], "source": ["import tushare as ts\n", "\n", "# ts.set_token(\"03f3feb2e93881ff4b517753ecd718d65c83bdfee22e094fbde8e75c\")\n", "pro = ts.pro_api()"]}, {"cell_type": "code", "execution_count": null, "id": "401fbe01", "metadata": {}, "outputs": [], "source": ["df = pro.daily(ts_code='000001.SZ')\n", "df.dtypes"]}, {"cell_type": "code", "execution_count": null, "id": "ad4f7c20", "metadata": {}, "outputs": [], "source": ["# 每日涨跌停价格\n", "df = pro.stk_limit(trade_date='20070104')\n", "df"]}, {"cell_type": "code", "execution_count": 2, "id": "5651593c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trade_date</th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>pe</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20250605</td>\n", "      <td>301590.SZ</td>\n", "      <td>N优优</td>\n", "      <td>电气设备</td>\n", "      <td>25.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20250605</td>\n", "      <td>837748.BJ</td>\n", "      <td>路桥信息</td>\n", "      <td>软件服务</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20250605</td>\n", "      <td>300513.SZ</td>\n", "      <td>恒实科技</td>\n", "      <td>软件服务</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20250605</td>\n", "      <td>300368.SZ</td>\n", "      <td>汇金股份</td>\n", "      <td>IT设备</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20250605</td>\n", "      <td>300082.SZ</td>\n", "      <td>奥克股份</td>\n", "      <td>化工原料</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5415</th>\n", "      <td>20250605</td>\n", "      <td>920027.BJ</td>\n", "      <td>交大铁发</td>\n", "      <td>运输设备</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5416</th>\n", "      <td>20250605</td>\n", "      <td>688775.SH</td>\n", "      <td>影石创新</td>\n", "      <td>IT设备</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5417</th>\n", "      <td>20250605</td>\n", "      <td>603400.SH</td>\n", "      <td>华之杰</td>\n", "      <td>机械基件</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5418</th>\n", "      <td>20250605</td>\n", "      <td>603382.SH</td>\n", "      <td>海阳科技</td>\n", "      <td>化纤</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5419</th>\n", "      <td>20250605</td>\n", "      <td>301678.SZ</td>\n", "      <td>新恒汇</td>\n", "      <td>半导体</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5420 rows × 5 columns</p>\n", "</div>"], "text/plain": ["     trade_date    ts_code  name industry     pe\n", "0      20250605  301590.SZ   N优优     电气设备  25.85\n", "1      20250605  837748.BJ  路桥信息     软件服务   0.00\n", "2      20250605  300513.SZ  恒实科技     软件服务   0.00\n", "3      20250605  300368.SZ  汇金股份     IT设备   0.00\n", "4      20250605  300082.SZ  奥克股份     化工原料   0.00\n", "...         ...        ...   ...      ...    ...\n", "5415   20250605  920027.BJ  交大铁发     运输设备   0.00\n", "5416   20250605  688775.SH  影石创新     IT设备   0.00\n", "5417   20250605  603400.SH   华之杰     机械基件   0.00\n", "5418   20250605  603382.SH  海阳科技       化纤   0.00\n", "5419   20250605  301678.SZ   新恒汇      半导体   0.00\n", "\n", "[5420 rows x 5 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pro.bak_basic(trade_date='20250605', fields='trade_date,ts_code,name,industry,pe')\n", "df"]}, {"cell_type": "code", "execution_count": 4, "id": "48a73297", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trade_date</th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>pe</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2708</th>\n", "      <td>20250605</td>\n", "      <td>603151.SH</td>\n", "      <td>邦基科技</td>\n", "      <td>饲料</td>\n", "      <td>25.68</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     trade_date    ts_code  name industry     pe\n", "2708   20250605  603151.SH  邦基科技       饲料  25.68"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["a = df[df['ts_code'] == '603151.SH']\n", "a"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}