"""
HTML可视化输出
"""

import base64
import io
import warnings
from datetime import datetime
from typing import Any, Dict, Optional

import matplotlib.dates as mdates
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from .statistics import Statistics

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class Visualizer:
    """可视化器"""

    def __init__(self):
        self.template = self._get_html_template()

    def generate_report(self, results: Dict[str, Any], output_file: str):
        """
        生成HTML报告

        Args:
            results: 回测结果
            output_file: 输出文件路径
        """
        print(f'正在生成HTML报告: {output_file}')

        # 生成各种图表
        equity_curve_img = self._plot_equity_curve(results)
        drawdown_img = self._plot_drawdown(results)
        monthly_returns_img = self._plot_monthly_returns(results)

        # 生成统计表格
        performance_table = self._generate_performance_table(results)
        trade_table = self._generate_trade_table(results)
        position_table = self._generate_position_table(results)

        # 填充HTML模板
        html_content = self.template.format(
            title='量化回测报告',
            equity_curve_img=equity_curve_img,
            drawdown_img=drawdown_img,
            monthly_returns_img=monthly_returns_img,
            performance_table=performance_table,
            trade_table=trade_table,
            position_table=position_table,
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        )

        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f'HTML报告已生成: {output_file}')

    def _plot_equity_curve(self, results: Dict[str, Any]) -> str:
        """绘制权益曲线"""
        portfolio_history = results.get('portfolio_history')
        benchmark_returns = results.get('benchmark_returns')

        if portfolio_history is None or portfolio_history.empty:
            return ''

        fig, ax = plt.subplots(figsize=(12, 6))

        # 绘制策略权益曲线
        dates = pd.to_datetime(portfolio_history['timestamp'])
        values = portfolio_history['total_value']
        ax.plot(dates, values, label='策略', linewidth=2, color='blue')

        # 绘制基准曲线（如果有）
        if benchmark_returns is not None and not benchmark_returns.empty:
            # 计算基准累积收益
            initial_value = portfolio_history['total_value'].iloc[0]
            benchmark_cumulative = initial_value * (1 + benchmark_returns).cumprod()
            ax.plot(dates, benchmark_cumulative, label='基准', linewidth=2, color='red', alpha=0.7)

        ax.set_title('权益曲线', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期')
        ax.set_ylabel('资产价值')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 格式化x轴日期
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)

        plt.tight_layout()

        return self._fig_to_base64(fig)

    def _plot_drawdown(self, results: Dict[str, Any]) -> str:
        """绘制回撤图"""
        portfolio_history = results.get('portfolio_history')

        if portfolio_history is None or portfolio_history.empty:
            return ''

        # 计算回撤
        values = portfolio_history['total_value']
        running_max = values.expanding().max()
        drawdown = (values - running_max) / running_max

        fig, ax = plt.subplots(figsize=(12, 4))

        dates = pd.to_datetime(portfolio_history['timestamp'])
        ax.fill_between(dates, drawdown, 0, alpha=0.3, color='red')
        ax.plot(dates, drawdown, color='red', linewidth=1)

        ax.set_title('回撤曲线', fontsize=14, fontweight='bold')
        ax.set_xlabel('日期')
        ax.set_ylabel('回撤比例')
        ax.grid(True, alpha=0.3)

        # 格式化y轴为百分比
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))

        # 格式化x轴日期
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
        plt.xticks(rotation=45)

        plt.tight_layout()

        return self._fig_to_base64(fig)

    def _plot_monthly_returns(self, results: Dict[str, Any]) -> str:
        """绘制月度收益率"""
        portfolio_history = results.get('portfolio_history')

        if portfolio_history is None or portfolio_history.empty:
            return ''

        # 计算月度收益率
        stats = Statistics()
        monthly_stats = stats.calculate_monthly_returns(portfolio_history)

        if monthly_stats.empty:
            return ''

        fig, ax = plt.subplots(figsize=(12, 6))

        # 绘制月度收益率柱状图
        colors = ['green' if x > 0 else 'red' for x in monthly_stats['return']]
        bars = ax.bar(range(len(monthly_stats)), monthly_stats['return'], color=colors, alpha=0.7)

        ax.set_title('月度收益率', fontsize=14, fontweight='bold')
        ax.set_xlabel('月份')
        ax.set_ylabel('收益率')
        ax.grid(True, alpha=0.3, axis='y')

        # 设置x轴标签
        ax.set_xticks(range(len(monthly_stats)))
        ax.set_xticklabels(monthly_stats['month'], rotation=45)

        # 格式化y轴为百分比
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))

        # 添加零线
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        plt.tight_layout()

        return self._fig_to_base64(fig)

    def _generate_performance_table(self, results: Dict[str, Any]) -> str:
        """生成性能统计表格"""
        performance_stats = results.get('performance_stats', {})

        if not performance_stats:
            return '<p>无性能统计数据</p>'

        table_html = "<table class='table table-striped'><thead><tr><th>指标</th><th>数值</th></tr></thead><tbody>"

        # 定义指标显示格式
        metrics = [
            ('总收益率', 'total_return', '{:.2%}'),
            ('年化收益率', 'annual_return', '{:.2%}'),
            ('年化波动率', 'annual_volatility', '{:.2%}'),
            ('夏普比率', 'sharpe_ratio', '{:.4f}'),
            ('最大回撤', 'max_drawdown', '{:.2%}'),
            ('胜率', 'win_rate', '{:.2%}'),
            ('平均盈利', 'avg_win', '{:.2%}'),
            ('平均亏损', 'avg_loss', '{:.2%}'),
            ('95% VaR', 'var_95', '{:.2%}'),
            ('总交易次数', 'total_trades', '{:.0f}'),
            ('盈利交易', 'winning_trades', '{:.0f}'),
            ('亏损交易', 'losing_trades', '{:.0f}'),
        ]

        for name, key, fmt in metrics:
            if key in performance_stats:
                value = performance_stats[key]
                if isinstance(value, (int, float)) and not np.isnan(value):
                    formatted_value = fmt.format(value)
                else:
                    formatted_value = 'N/A'
                table_html += f'<tr><td>{name}</td><td>{formatted_value}</td></tr>'

        # 如果有基准比较数据
        if 'benchmark_total_return' in performance_stats:
            table_html += "<tr><td colspan='2'><strong>基准比较</strong></td></tr>"
            benchmark_metrics = [
                ('基准总收益率', 'benchmark_total_return', '{:.2%}'),
                ('基准年化收益率', 'benchmark_annual_return', '{:.2%}'),
                ('超额收益', 'excess_return', '{:.2%}'),
                ('跟踪误差', 'tracking_error', '{:.2%}'),
                ('信息比率', 'information_ratio', '{:.4f}'),
                ('Beta系数', 'beta', '{:.4f}'),
                ('Alpha', 'alpha', '{:.2%}'),
            ]

            for name, key, fmt in benchmark_metrics:
                if key in performance_stats:
                    value = performance_stats[key]
                    if isinstance(value, (int, float)) and not np.isnan(value):
                        formatted_value = fmt.format(value)
                    else:
                        formatted_value = 'N/A'
                    table_html += f'<tr><td>{name}</td><td>{formatted_value}</td></tr>'

        table_html += '</tbody></table>'

        return table_html

    def _generate_trade_table(self, results: Dict[str, Any]) -> str:
        """生成交易记录表格"""
        trade_records = results.get('trade_records', [])

        if not trade_records:
            return '<p>无交易记录</p>'

        # 只显示最近的20条交易记录
        recent_trades = trade_records[-20:] if len(trade_records) > 20 else trade_records

        table_html = "<table class='table table-striped table-sm'><thead><tr>"
        table_html += '<th>时间</th><th>股票</th><th>方向</th><th>数量</th><th>价格</th><th>手续费</th></tr></thead><tbody>'

        for trade in recent_trades:
            timestamp = (
                trade['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if 'timestamp' in trade else 'N/A'
            )
            symbol = trade.get('symbol', 'N/A')
            side = '买入' if trade.get('side') == 'buy' else '卖出'
            quantity = f'{trade.get("quantity", 0):.0f}'
            price = f'{trade.get("price", 0):.2f}'
            commission = f'{trade.get("commission", 0):.2f}'

            table_html += f'<tr><td>{timestamp}</td><td>{symbol}</td><td>{side}</td>'
            table_html += f'<td>{quantity}</td><td>{price}</td><td>{commission}</td></tr>'

        table_html += '</tbody></table>'

        if len(trade_records) > 20:
            table_html += f'<p><small>显示最近20条交易记录，总共{len(trade_records)}条</small></p>'

        return table_html

    def _generate_position_table(self, results: Dict[str, Any]) -> str:
        """生成持仓表格"""
        final_positions = results.get('final_positions', {})

        if not final_positions:
            return '<p>无持仓记录</p>'

        table_html = "<table class='table table-striped'><thead><tr>"
        table_html += '<th>股票</th><th>数量</th><th>成本价</th><th>当前价</th><th>市值</th><th>盈亏</th></tr></thead><tbody>'

        for symbol, position in final_positions.items():
            volume_display = f'{position.total_volume:.0f}'
            avg_cost = f'{position.avg_cost:.2f}'
            current_price = f'{position.price:.2f}'
            market_value = f'{position.value:.2f}'
            pnl = f'{position.pnl:.2f}'
            pnl_color = (
                'color: green;' if position.pnl > 0 else 'color: red;' if position.pnl < 0 else ''
            )

            table_html += f'<tr><td>{symbol}</td><td>{volume_display}</td><td>{avg_cost}</td>'
            table_html += f"<td>{current_price}</td><td>{market_value}</td><td style='{pnl_color}'>{pnl}</td></tr>"

        table_html += '</tbody></table>'

        return table_html

    def _fig_to_base64(self, fig) -> str:
        """将matplotlib图形转换为base64字符串"""
        buffer = io.BytesIO()
        fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close(fig)
        return f'data:image/png;base64,{image_base64}'

    def _get_html_template(self) -> str:
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; }}
        .container {{ max-width: 1200px; }}
        .chart-container {{ margin: 20px 0; text-align: center; }}
        .chart-container img {{ max-width: 100%; height: auto; }}
        .table-container {{ margin: 20px 0; }}
        .performance-summary {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">{title}</h1>
                <p class="text-center text-muted">生成时间: {generation_time}</p>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h2>权益曲线</h2>
                <div class="chart-container">
                    <img src="{equity_curve_img}" alt="权益曲线">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h2>回撤分析</h2>
                <div class="chart-container">
                    <img src="{drawdown_img}" alt="回撤曲线">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h2>月度收益率</h2>
                <div class="chart-container">
                    <img src="{monthly_returns_img}" alt="月度收益率">
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h2>性能统计</h2>
                <div class="table-container">
                    {performance_table}
                </div>
            </div>
            <div class="col-md-6">
                <h2>最终持仓</h2>
                <div class="table-container">
                    {position_table}
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h2>交易记录</h2>
                <div class="table-container">
                    {trade_table}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
        """
