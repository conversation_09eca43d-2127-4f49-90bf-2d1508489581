import quantback
from quantback.single_bt import Backtest, Strategy
from quantback.single_bt.lib import crossover
from quantback.single_bt.test import GOOG, SMA


class SmaCross(Strategy):
    # Define the two MA lags as *class variables*
    # for later optimization
    # 在Strategy初始化时如果没有提供n1,n2,则使用默认值10,20.
    n1 = 10
    n2 = 20

    def init(self):
        # Precompute the two moving averages
        self.sma1 = self.I(SMA, self.data.Close, self.n1)
        self.sma2 = self.I(SMA, self.data.Close, self.n2)

    def next(self):
        # If sma1 crosses above sma2, close any existing
        # short trades, and buy the asset
        if crossover(self.sma1, self.sma2):
            self.position.close()
            self.buy()

        # Else, if sma1 crosses below sma2, close any existing
        # long trades, and sell the asset
        elif crossover(self.sma2, self.sma1):
            self.position.close()
            self.sell()


# 如果 bt.optimize用method='grid',就会调用multiprocessing.
# 在Windows上，使用 multiprocessing 模块时，必须将创建和启动进程的代码
# 放在 if __name__ == '__main__': 语句块中(只让主进程执行这里)，以避免出现递归创建进程的问题。
# 如果 bt.optimize用method='sambo',实测不会出现重复执行__init__.py文件的问题.
if __name__ == "__main__":
    quantback.setup_logging()
    bt = Backtest(GOOG, SmaCross, cash=1_000_000, commission=0.000336, trade_on_close=True)
    # stats = bt.run()
    stats = bt.optimize(
        n1=range(5, 30, 5),
        n2=range(10, 70, 5),
        maximize="Equity Final [$]",
        constraint=lambda param: param.n1 < param.n2,
        method='sambo',
    )
    print(stats)
    print(stats._strategy)
    bt.plot(open_browser=True)
