import logging
import os
import zipfile
from datetime import datetime

import pandas as pd

import quantback
from quantback.database.clickhouse_client import get_clickhouse_client
from quantback.utils import convert_stock_code
from quantback.utils.progress import TextProgress

logger = logging.getLogger(__name__)


def import_stock_finance_snapshot(zip_path):
    """
    从ZIP文件中解析CSV数据并导入股票财务快照数据到数据库

    Args:
        zip_path: ZIP文件路径
    """
    logger.info('开始导入股票财务快照数据')

    # 检查ZIP文件是否存在
    if not os.path.exists(zip_path):
        logger.error(f'ZIP文件不存在: {zip_path}')
        return False

    # 连接数据库
    try:
        client = get_clickhouse_client()
    except Exception as e:
        logger.error(f'连接数据库失败: {str(e)}')
        return False

    success_count = 0
    failed_count = 0
    total_records = 0

    try:
        # 打开ZIP文件并处理所有CSV文件
        with zipfile.ZipFile(zip_path, 'r') as zip_file:
            # 获取所有CSV文件并排序
            csv_files = [f for f in zip_file.namelist() if f.endswith('.csv')]
            csv_files.sort()

            if not csv_files:
                logger.info('ZIP文件中没有找到CSV文件')
                return True

            logger.info(f'从ZIP文件中找到 {len(csv_files)} 个CSV文件')
            logger.info(f'需要处理的CSV文件数量: {len(csv_files)}')

            # 处理每个CSV文件
            for csv_file in TextProgress(csv_files, desc='处理股票财务快照数据', show_time=True):
                try:
                    # 从文件名提取交易日期 (如: 2005-01-05.csv)
                    trade_date_str = csv_file.replace('.csv', '')
                    trade_date = datetime.strptime(trade_date_str, '%Y-%m-%d').date()

                    print(f'正在处理文件: {csv_file}')

                    # 从ZIP文件中读取CSV数据
                    with zip_file.open(csv_file) as csv_data:
                        df = pd.read_csv(csv_data, index_col=0)

                    if df is not None and not df.empty:
                        # 添加交易日期字段
                        df['trade_date'] = trade_date

                        # 重命名索引为stock_code
                        df.reset_index(inplace=True)
                        df.rename(columns={df.columns[0]: 'stock_code'}, inplace=True)

                        # 应用convert_stock_code函数转换股票代码
                        df['stock_code'] = df['stock_code'].apply(convert_stock_code)

                        # 定义字段映射，确保与数据库表结构一致
                        clickhouse_columns = [
                            'trade_date',  # 交易日期
                            'stock_code',  # 股票代码
                            'capitalization',  # 总股本(万股)
                            'circulating_cap',  # 流通股本(万股)
                            'market_cap',  # 总市值(亿元)
                            'circulating_market_cap',  # 流通市值(亿元)
                            'pe_ratio',  # 动态市盈率
                            'pb_ratio',  # 市净率
                            'eps',  # 每股收益(元)
                            'roe',  # 净资产收益率(%)
                            'roa',  # 总资产收益率(%)
                        ]

                        # 插入数据到数据库
                        client.insert_df(
                            'stock_finance_snapshot',
                            df[clickhouse_columns],
                            column_names=clickhouse_columns,
                        )

                        record_count = len(df)
                        total_records += record_count
                        success_count += 1

                        print(f'成功处理 {csv_file}: {record_count} 条记录')

                    else:
                        print(f'文件 {csv_file} 无数据')
                        success_count += 1

                except Exception as e:
                    failed_count += 1
                    print(f'处理文件 {csv_file} 失败: {str(e)}')
                    continue

        # 统计结果
        logger.info('股票财务快照数据导入完成！')
        logger.info(f'总文件数: {len(csv_files)}')
        logger.info(f'成功: {success_count}')
        logger.info(f'失败: {failed_count}')
        logger.info(f'总记录数: {total_records}')

        return failed_count == 0

    except Exception as e:
        logger.error(f'导入股票财务快照数据时出错: {str(e)}')
        return False
    finally:
        # 不关闭数据库连接，复用连接
        pass


if __name__ == '__main__':
    # 配置日志
    quantback.setup_logging()

    # 配置ZIP文件路径
    zip_path = r'D:\stockdata\snapshot\finance20050105-20250606.zip'

    # 导入股票财务快照数据
    success = import_stock_finance_snapshot(zip_path)
    if success:
        logger.info('股票财务快照数据导入成功')
    else:
        logger.error('股票财务快照数据导入失败')
