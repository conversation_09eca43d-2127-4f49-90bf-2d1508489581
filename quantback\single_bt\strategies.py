from quantback.single_bt.backtesting import Strategy
from quantback.single_bt.lib import crossover
from quantback.indicators import CCI, DMI, KDJ, MA, MACD


class MAStrategy(Strategy):
    n1 = 10
    n2 = 20

    def init(self):
        self.sma1 = self.I(MA, self.data.Close, self.n1)
        self.sma2 = self.I(MA, self.data.Close, self.n2)

    def next(self):
        if crossover(self.sma1, self.sma2):
            self.position.close()
            self.buy()

        elif crossover(self.sma2, self.sma1):
            self.position.close()
            self.sell()


class MACDStrategy(Strategy):
    short_period = 12
    long_period = 26
    signal_period = 9

    def init(self):
        CLOSE = self.data.Close
        self.macd = self.I(
            MACD,
            CLOSE,
            self.short_period,
            self.long_period,
            self.signal_period,
            name=['DIF', 'DEA', 'MACD'],
        )

    def next(self):
        DIF, DEA, MACD_ = self.macd
        if crossover(MACD_, 0):
            self.position.close()
            self.buy()
        elif crossover(0, MACD_):
            self.position.close()
            self.sell()


class KDJStrategy(Strategy):
    k_period = 9
    d_period = 3
    j_period = 3

    def init(self):
        CLOSE = self.data.Close
        HIGH = self.data.High
        LOW = self.data.Low
        self.kdj = self.I(
            KDJ, CLOSE, HIGH, LOW, self.k_period, self.d_period, self.j_period, name=['K', 'D', 'J']
        )

    def next(self):
        # 注意kdj的买卖不是两个指标相互crossover,可能会出现连续多单或空单
        K, D, J = self.kdj
        if crossover(J, 0):
            self.position.close()
            self.buy()
        elif crossover(100, J):
            self.position.close()
            self.sell()


class CCIStrategy(Strategy):
    n = 14

    def init(self):
        CLOSE = self.data.Close
        HIGH = self.data.High
        LOW = self.data.Low
        self.CCI = self.I(CCI, CLOSE, HIGH, LOW, self.n)

    def next(self):
        if crossover(self.CCI, -100):
            self.position.close()
            self.buy()
        elif crossover(100, self.CCI):
            self.position.close()
            self.sell()


class DMIStrategy(Strategy):
    m1 = 14
    m2 = 6

    def init(self):
        CLOSE = self.data.Close
        HIGH = self.data.High
        LOW = self.data.Low
        self.DMI = self.I(
            DMI, CLOSE, HIGH, LOW, self.m1, self.m2, name=['PDI', 'MDI', 'ADX', 'ADXR']
        )

    def next(self):
        PDI, MDI, *_ = self.DMI
        if crossover(PDI, MDI):
            self.position.close()
            self.buy()
        elif crossover(MDI, PDI):
            self.position.close()
            self.sell()
