{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2013-02-25</th>\n", "      <td>802.3</td>\n", "      <td>808.41</td>\n", "      <td>790.49</td>\n", "      <td>790.77</td>\n", "      <td>2303900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2013-02-26</th>\n", "      <td>795.0</td>\n", "      <td>795.95</td>\n", "      <td>784.40</td>\n", "      <td>790.13</td>\n", "      <td>2202500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2013-02-27</th>\n", "      <td>794.8</td>\n", "      <td>804.75</td>\n", "      <td>791.11</td>\n", "      <td>799.78</td>\n", "      <td>2026100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2013-02-28</th>\n", "      <td>801.1</td>\n", "      <td>806.99</td>\n", "      <td>801.03</td>\n", "      <td>801.20</td>\n", "      <td>2265800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2013-03-01</th>\n", "      <td>797.8</td>\n", "      <td>807.14</td>\n", "      <td>796.15</td>\n", "      <td>806.19</td>\n", "      <td>2175400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Open    High     Low   Close   Volume\n", "2013-02-25  802.3  808.41  790.49  790.77  2303900\n", "2013-02-26  795.0  795.95  784.40  790.13  2202500\n", "2013-02-27  794.8  804.75  791.11  799.78  2026100\n", "2013-02-28  801.1  806.99  801.03  801.20  2265800\n", "2013-03-01  797.8  807.14  796.15  806.19  2175400"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from quantback.backtesting.test import GOOG\n", "\n", "GOOG.tail()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}