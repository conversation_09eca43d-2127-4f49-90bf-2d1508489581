import logging
import os
from datetime import datetime

import pandas as pd

import quantback
from quantback.database.clickhouse_client import get_clickhouse_client

logger = logging.getLogger(__name__)


def import_trading_calendar(file_path):
    """
    从文件中读取交易日历数据并导入到ClickHouse数据库

    Args:
        file_path: 交易日历文件路径，每行一个日期，格式为yyyy-mm-dd

    Returns:
        bool: 导入是否成功
    """

    logger.info(f'开始导入交易日历数据，文件路径: {file_path}')

    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f'交易日历文件不存在: {file_path}')
        return False

    try:
        # 读取交易日历文件
        trading_dates = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:  # 跳过空行
                    try:
                        # 验证日期格式
                        date_obj = datetime.strptime(line, '%Y-%m-%d')
                        trading_dates.append(date_obj.date())
                    except ValueError as e:
                        logger.warning(f'第{line_num}行日期格式错误: {line}, 错误: {str(e)}')
                        continue

        if not trading_dates:
            logger.error('未读取到有效的交易日期')
            return False

        logger.info(f'成功读取 {len(trading_dates)} 个交易日期')

        # 创建DataFrame
        df = pd.DataFrame({'trade_date': trading_dates})

        # 连接ClickHouse数据库
        try:
            client = get_clickhouse_client()
        except Exception as e:
            logger.error(f'连接ClickHouse数据库失败: {str(e)}')
            return False

        try:
            # 清空现有数据（可选，根据需要决定是否保留）
            logger.info('清空trading_calendar表中的现有数据...')
            client.command('TRUNCATE TABLE trading_calendar')

            # 插入数据到ClickHouse
            logger.info('开始插入交易日历数据到数据库...')

            # 指定字段映射确保数据正确插入
            clickhouse_columns = ['trade_date']

            client.insert_df(
                'trading_calendar', df[clickhouse_columns], column_names=clickhouse_columns
            )

            logger.info(f'成功插入 {len(df)} 条交易日历记录到数据库')

            # 验证插入结果
            result = client.query('SELECT COUNT(*) as count FROM trading_calendar')
            count = result.result_rows[0][0]
            logger.info(f'数据库中交易日历记录总数: {count}')

            return True

        except Exception as e:
            logger.error(f'插入交易日历数据到数据库失败: {str(e)}')
            return False
        finally:
            # 不关闭数据库连接，复用连接
            pass

    except Exception as e:
        logger.error(f'导入交易日历数据时出错: {str(e)}')
        return False


if __name__ == '__main__':
    # 配置日志
    quantback.setup_logging()

    # 交易日历文件路径
    trading_calendar_file = r'D:\stockdata\trading_calendar.txt'

    # 导入交易日历数据
    success = import_trading_calendar(trading_calendar_file)
    if success:
        logger.info('交易日历数据导入成功')
    else:
        logger.error('交易日历数据导入失败')
