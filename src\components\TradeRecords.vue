<template>
  <div class="card bg-base-100 border">
    <div class="card-body">
      <div v-if="!hasTrades">
        <h2 class="card-title">成交记录</h2>
        <div class="text-center py-8">
          <p class="text-gray-500">无成交记录</p>
        </div>
      </div>
      <div v-else>
        <div class="flex justify-between items-center mb-4">
          <h2 class="card-title">成交记录</h2>
          <div class="text-sm text-gray-500">共 {{ filteredTrades.length }} 条记录</div>
        </div>

        <!-- 过滤器 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label class="label">
              <span>股票代码</span>
            </label>
            <select class="select select-sm" v-model="filterSymbol" @change="currentPage = 1">
              <option value="">全部</option>
              <option v-for="symbol in uniqueSymbols" :key="symbol" :value="symbol">
                {{ symbol }}
              </option>
            </select>
          </div>

          <div>
            <label class="label">
              <span>操作类型</span>
            </label>
            <select class="select select-sm" v-model="filterAction" @change="currentPage = 1">
              <option value="">全部</option>
              <option v-for="action in uniqueActions" :key="action" :value="action">
                {{ getSideText(action) }}
              </option>
            </select>
          </div>

          <div>
            <label class="label">
              <span>操作</span>
            </label>
            <button class="btn btn-outline btn-sm" @click="clearFilters">清除过滤</button>
          </div>
        </div>

        <!-- 表格 -->
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full">
            <thead>
              <tr>
                <th>日期</th>
                <th>股票代码</th>
                <th>操作</th>
                <th>数量</th>
                <th>成交价格</th>
                <th>开仓价格</th>
                <th>金额</th>
                <th>盈亏金额</th>
                <th>盈亏比例</th>
                <th>手续费</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(trade, index) in currentData" :key="index">
                <td class="font-mono">{{ formatDate(trade.timestamp) }}</td>
                <td class="font-mono font-semibold">{{ trade.symbol }}</td>
                <td>
                  <div :class="`badge ${getActionBadge(trade.side)}`">
                    {{ getSideText(trade.side) }}
                  </div>
                </td>
                <td class="font-mono">{{ formatNumber(Math.abs(trade.volume || 0)) }}</td>
                <td class="font-mono">{{ formatCurrency(trade.price || 0) }}</td>
                <td class="font-mono">
                  {{
                    trade.side === 'sell' && trade.open_price > 0
                      ? formatCurrency(trade.open_price)
                      : '-'
                  }}
                </td>
                <td :class="`font-mono ${getActionColor(trade.side)}`">
                  {{ formatCurrency(Math.abs((trade.volume || 0) * (trade.price || 0))) }}
                </td>
                <td class="font-mono" :class="getPnlColor(calculatePnlAmount(trade))">
                  {{ formatPnlAmount(trade) }}
                </td>
                <td class="font-mono" :class="getPnlColor(calculatePnlAmount(trade))">
                  {{ formatPnlPercent(trade) }}
                </td>
                <td class="font-mono text-warning">{{ formatCurrency(trade.commission) }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页控件 -->
        <div v-if="totalPages > 1" class="flex justify-center mt-4">
          <div class="join">
            <button
              class="join-item btn btn-sm"
              @click="currentPage = Math.max(1, currentPage - 1)"
              :disabled="currentPage === 1"
            >
              «
            </button>

            <!-- 页码按钮 -->
            <button
              v-for="pageNum in visiblePages"
              :key="pageNum"
              :class="`join-item btn btn-sm ${currentPage === pageNum ? 'btn-active' : ''}`"
              @click="currentPage = pageNum"
            >
              {{ pageNum }}
            </button>

            <button
              class="join-item btn btn-sm"
              @click="currentPage = Math.min(totalPages, currentPage + 1)"
              :disabled="currentPage === totalPages"
            >
              »
            </button>
          </div>
        </div>

        <!-- 分页信息 -->
        <div class="text-center text-sm text-gray-500 mt-2">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  trades: {
    type: Array,
    default: () => [],
  },
})

const currentPage = ref(1)
const itemsPerPage = 20
const filterSymbol = ref('')
const filterAction = ref('')

const hasTrades = computed(() => props.trades && props.trades.length > 0)

const filteredTrades = computed(() => {
  if (!hasTrades.value) return []

  return props.trades
    .filter((trade) => {
      // 添加安全检查，防止 undefined 错误
      const symbol = trade.symbol || ''
      const side = trade.side || ''

      const symbolMatch =
        !filterSymbol.value || symbol.toLowerCase().includes(filterSymbol.value.toLowerCase())
      const actionMatch = !filterAction.value || side === filterAction.value
      return symbolMatch && actionMatch
    })
    .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0)) // 最新的在前（按时间戳排序）
})

const totalPages = computed(() => Math.ceil(filteredTrades.value.length / itemsPerPage))

const currentData = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  return filteredTrades.value.slice(startIndex, endIndex)
})

const uniqueSymbols = computed(() => {
  if (!hasTrades.value) return []
  return [...new Set(props.trades.map((trade) => trade.symbol))].sort()
})

const uniqueActions = computed(() => {
  if (!hasTrades.value) return []
  return [...new Set(props.trades.map((trade) => trade.side || '').filter(Boolean))].sort()
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  for (let i = 0; i < Math.min(5, total); i++) {
    let pageNum
    if (total <= 5) {
      pageNum = i + 1
    } else if (current <= 3) {
      pageNum = i + 1
    } else if (current >= total - 2) {
      pageNum = total - 4 + i
    } else {
      pageNum = current - 2 + i
    }
    pages.push(pageNum)
  }

  return pages
})

const formatCurrency = (value) => {
  return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

const formatNumber = (value) => {
  return value.toLocaleString('zh-CN')
}

const formatDate = (timestamp) => {
  // 使用 UTC 时区格式化日期，不添加时区偏移
  return new Date(timestamp).toLocaleDateString('zh-CN', { timeZone: 'UTC' })
}

const getSideText = (side) => {
  if (side === 'buy') return '买入'
  if (side === 'sell') return '卖出'
  return side || '-'
}

const calculatePnlAmount = (trade) => {
  // 只有卖出交易且有开仓价格时才计算盈亏
  if (trade.side !== 'sell' || !trade.open_price || trade.open_price <= 0) {
    return 0
  }

  const volume = trade.volume || 0
  const sellPrice = trade.price || 0
  const openPrice = trade.open_price || 0
  const commission = trade.commission || 0

  // 盈亏金额 = (卖出价格 - 开仓价格) × 数量 - 手续费
  return (sellPrice - openPrice) * volume - commission
}

const calculatePnlPercent = (trade) => {
  // 只有卖出交易且有开仓价格时才计算盈亏比例
  if (trade.side !== 'sell' || !trade.open_price || trade.open_price <= 0) {
    return 0
  }

  const sellPrice = trade.price || 0
  const openPrice = trade.open_price || 0

  // 盈亏比例 = (卖出价格 - 开仓价格) / 开仓价格
  return (sellPrice - openPrice) / openPrice
}

const formatPnlAmount = (trade) => {
  const pnlAmount = calculatePnlAmount(trade)
  if (trade.side !== 'sell' || !trade.open_price || trade.open_price <= 0) {
    return '-'
  }
  return formatCurrency(pnlAmount)
}

const formatPnlPercent = (trade) => {
  const pnlPercent = calculatePnlPercent(trade)
  if (trade.side !== 'sell' || !trade.open_price || trade.open_price <= 0) {
    return '-'
  }
  return `${(pnlPercent * 100).toFixed(2)}%`
}

const getPnlColor = (pnlAmount) => {
  if (pnlAmount > 0) return 'text-success'
  if (pnlAmount < 0) return 'text-error'
  return 'text-gray-500'
}

const getActionColor = (side) => {
  if (!side) return 'text-gray-500'
  switch (side.toLowerCase()) {
    case 'buy':
    case '买入':
      return 'text-success'
    case 'sell':
    case '卖出':
      return 'text-error'
    default:
      return 'text-gray-500'
  }
}

const getActionBadge = (side) => {
  if (!side) return 'badge-neutral'
  switch (side.toLowerCase()) {
    case 'buy':
    case '买入':
      return 'badge-success'
    case 'sell':
    case '卖出':
      return 'badge-error'
    default:
      return 'badge-neutral'
  }
}

const clearFilters = () => {
  filterSymbol.value = ''
  filterAction.value = ''
  currentPage.value = 1
}
</script>
