import logging
import os

import pandas as pd
from tqdm import tqdm
from xtquant import xtdata

import quantback
from quantback.database.clickhouse_client import get_clickhouse_client
from quantback.utils import convert_stock_code

logger = logging.getLogger(__name__)


def read_stock_list(file_path):
    """
    从文件中读取股票代码列表

    Args:
        file_path: 股票代码文件路径

    Returns:
        list: 转换后的股票代码列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            stock_codes = []
            for line in f:
                stripped_line = line.strip()
                if stripped_line:
                    stock_codes.append(stripped_line)

        # 转换股票代码格式
        converted_codes = [convert_stock_code(code) for code in stock_codes]

        logger.info(f'从 {file_path} 读取到 {len(converted_codes)} 只股票代码')
        return converted_codes

    except Exception as e:
        logger.error(f'读取股票代码文件失败: {str(e)}')
        return []


def download_dividend_info(stock_list_files, start_time='19900101', end_time=''):
    """
    下载股票分红送转数据到ClickHouse数据库

    Args:
        stock_list_files: 股票代码文件路径列表
        start_time: 开始时间，格式YYYYMMDD
        end_time: 结束时间，格式YYYYMMDD，默认为当前日期
    """

    logger.info(f'开始下载分红送转数据，时间范围: {start_time} - {end_time}')

    # 读取所有股票代码列表
    all_stock_codes = []
    for stock_list_file in stock_list_files:
        stock_codes = read_stock_list(stock_list_file)
        if stock_codes:
            all_stock_codes.extend(stock_codes)
        else:
            logger.warning(f'从文件 {stock_list_file} 未能读取到股票代码')

    # 去重股票代码
    all_stock_codes = list(set(all_stock_codes))

    if not all_stock_codes:
        logger.error('未能读取到任何股票代码，退出')
        return False

    # 连接ClickHouse数据库
    try:
        client = get_clickhouse_client()
    except Exception as e:
        logger.error(f'连接ClickHouse数据库失败: {str(e)}')
        return False

    success_count = 0
    failed_count = 0
    total_records = 0

    try:
        # 清空dividend_info表，因为每次导入完整数据
        logger.info('清空dividend_info表...')
        client.command('TRUNCATE TABLE dividend_info')
        logger.info('dividend_info表已清空')
        # 使用tqdm显示进度
        for stock_code in tqdm(all_stock_codes, desc='下载分红送转数据'):
            try:
                # 调用迅投接口获取分红送转数据
                df = xtdata.get_divid_factors(
                    stock_code=stock_code, start_time=start_time, end_time=end_time
                )

                if df is not None and not df.empty:
                    # 转换数据格式（内联transform_dividend_data函数体）
                    # 将index按yyyymmdd格式解析为日期，作为ex_date
                    df['ex_date'] = pd.to_datetime(df.index, format='%Y%m%d').date

                    # 添加股票代码
                    df['stock_code'] = stock_code

                    # 重命名列以匹配数据库表结构（忽略time字段）
                    column_mapping = {
                        'interest': 'interest',  # 每股股利（税前，元）
                        'stockBonus': 'stock_bonus',  # 每股红股（股）
                        'stockGift': 'stock_gift',  # 每股转增股本（股）
                        'allotNum': 'allot_num',  # 每股配股数（股）
                        'allotPrice': 'allot_price',  # 配股价格（元）
                        'gugai': 'gugai',  # 是否股改
                        'dr': 'dr',  # 除权系数
                    }

                    # 重命名列
                    transformed_df = df.rename(columns=column_mapping)

                    if not transformed_df.empty:
                        # 获取ClickHouse表的字段映射配置
                        clickhouse_columns = [
                            'stock_code',  # 股票代码
                            'ex_date',  # 除权除息日期
                            'interest',  # 每股股利（税前，元）
                            'stock_bonus',  # 每股红股（股）
                            'stock_gift',  # 每股转增股本（股）
                            'allot_num',  # 每股配股数（股）
                            'allot_price',  # 配股价格（元）
                            'gugai',  # 是否股改
                            'dr',  # 除权系数
                        ]

                        # 插入数据库，指定column_names确保字段映射正确
                        client.insert_df(
                            'dividend_info',
                            transformed_df[clickhouse_columns],
                            column_names=clickhouse_columns,
                        )

                        record_count = len(transformed_df)
                        total_records += record_count

                        tqdm.write(f'成功处理 {stock_code}: {record_count} 条记录')
                    else:
                        tqdm.write(f'股票 {stock_code} 无分红送转数据')
                else:
                    tqdm.write(f'股票 {stock_code} 无分红送转数据')

                success_count += 1

            except Exception as e:
                failed_count += 1
                tqdm.write(f'处理股票 {stock_code} 失败: {str(e)}')
                continue

        # 统计结果
        logger.info('分红送转数据下载完成！')
        logger.info(f'总股票数: {len(all_stock_codes)}')
        logger.info(f'成功: {success_count}')
        logger.info(f'失败: {failed_count}')
        logger.info(f'总记录数: {total_records}')

        return failed_count == 0

    except Exception as e:
        logger.error(f'下载分红送转数据时出错: {str(e)}')
        return False
    finally:
        # 不关闭数据库连接，复用连接
        pass


if __name__ == '__main__':
    # 配置日志
    quantback.setup_logging()

    # 股票代码文件路径列表, 包含etf
    stock_list_files = [r'D:\stockdata\stock_list.txt', r'D:\stockdata\etf_list.txt']

    # 检查文件是否存在
    valid_files = []
    for stock_list_file in stock_list_files:
        if os.path.exists(stock_list_file):
            valid_files.append(stock_list_file)
        else:
            logger.warning(f'股票代码文件不存在: {stock_list_file}')

    if not valid_files:
        logger.error('没有找到任何有效的股票代码文件')
    else:
        # 下载分红送转数据
        success = download_dividend_info(valid_files)
        if success:
            logger.info('所有分红送转数据下载成功')
        else:
            logger.warning('部分分红送转数据下载失败')
