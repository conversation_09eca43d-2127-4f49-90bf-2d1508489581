import logging
import os
from typing import Dict, List, Tuple

import duckdb
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from tslearn.clustering import TimeSeriesKMeans
from tslearn.utils import to_time_series_dataset  # 此处加载比较耗时

import quantback
from quantback.market_interface.data_mgr import query_kline_from_duckdb

logger = logging.getLogger(__name__)
# 处理中文显示问题
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False


def get_stock_list() -> List[str]:
    """
    从DuckDB的stock_list表中获取沪深A股的股票列表

    Returns:
        List[str]: 沪深A股股票代码列表
    """
    try:
        # 获取配置信息

        config = quantback.get_config()
        db_path = config.get('duckdb.path')

        # 连接DuckDB数据库
        conn = duckdb.connect(db_path)

        # 查询沪深A股的股票列表
        query = """
        SELECT stock_code 
        FROM stock_list 
        WHERE sector = '沪深A股'
        ORDER BY stock_code
        """

        result = conn.execute(query).fetchdf()
        conn.close()

        if result.empty:
            logger.warning("未从DuckDB获取到沪深A股股票列表，请确保已执行save_stocks命令")
            return []

        stock_list = result['stock_code'].tolist()
        logger.info(f"从DuckDB获取到沪深A股股票列表，共 {len(stock_list)} 只股票")
        return stock_list

    except Exception as e:
        logger.error(f"从DuckDB获取沪深A股股票列表时出错: {str(e)}")
        return []


def preprocess_stock_data(
    stock_list: List[str],
    benchmark_code: str = '000001.SH',
    start_time: str = '20240101',
    end_time: str = '20241231',
    period='1d',
) -> Dict[str, pd.DataFrame]:
    """
    处理股票数据，计算日收益率和累计收益率
    不再进行reindex操作，因为DTW聚类算法可以处理不等长序列

    Args:
        stock_list: 股票代码列表
        benchmark_code: 基准指数代码
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD

    Returns:
        Dict[str, pd.DataFrame]: 股票代码到DataFrame的映射
    """
    try:
        # 添加基准指数到股票列表中
        all_stocks = stock_list.copy()
        if benchmark_code not in all_stocks:
            all_stocks.append(benchmark_code)

        # 获取所有股票的K线数据
        stock_dfs = {}

        for stock_code in all_stocks:
            try:
                # 获取股票的日K线数据
                df = query_kline_from_duckdb(
                    stock_code=stock_code, start_time=start_time, end_time=end_time, period=period
                )

                if df is None or df.empty:
                    logger.warning(f"股票 {stock_code} 没有数据")
                    continue

                # 设置日期为索引
                df.set_index('trade_dt', inplace=True)

                # 计算日收益率
                df['daily_return'] = df['close'].pct_change()

                # 计算累计收益率, 以1为起点
                df['cumulative_return'] = (1 + df['daily_return']).cumprod()
                df.iloc[0, df.columns.get_loc('cumulative_return')] = 1

                stock_dfs[stock_code] = df

            except Exception as e:
                logger.error(f"处理股票 {stock_code} 时出错: {str(e)}")

        # 检查是否成功获取基准指数数据
        if benchmark_code not in stock_dfs:
            logger.error(f"无法获取基准指数 {benchmark_code} 的数据")

        return stock_dfs

    except Exception as e:
        logger.error(f"预处理股票数据时出错: {str(e)}")
        return {}


def cluster_stocks(
    stock_dfs: Dict[str, pd.DataFrame], n_clusters: int = 5
) -> Tuple[Dict[int, List[str]], np.ndarray]:
    """
    使用tslearn的TimeSeriesKMeans对股票的累计收益率曲线进行聚类
    DTW距离可以处理不等长的时间序列

    Args:
        stock_dfs: 股票代码到DataFrame的映射，每个DataFrame包含累计收益率曲线
        n_clusters: 聚类数量

    Returns:
        Tuple[Dict[int, List[str]], np.ndarray]:
            - 聚类簇到股票代码列表的映射
            - 聚类中心(barycenter)矩阵
    """
    try:
        logger.info(f"开始使用DTW距离进行时间序列聚类，聚类数量: {n_clusters}")

        # 准备数据：提取每只股票的cumulative_return序列
        # 排除上证指数
        stock_codes = [code for code in stock_dfs.keys() if code != '000001.SH']

        # 检查是否有足够的股票进行聚类
        if len(stock_codes) < n_clusters:
            logger.warning(
                f"股票数量({len(stock_codes)})小于聚类数量({n_clusters})，将聚类数量调整为 {len(stock_codes)}"
            )
            n_clusters = len(stock_codes)

        # 确保所有时间序列至少有2个点(为DTW算法要求)
        valid_stocks = []
        for code in stock_codes:
            series = stock_dfs[code]['cumulative_return']
            if len(series) >= 2:
                valid_stocks.append(code)
            else:
                logger.warning(f"股票 {code} 的时间序列长度不足2，将被排除在聚类之外")

        if len(valid_stocks) < n_clusters:
            logger.warning(
                f"有效股票数量({len(valid_stocks)})小于聚类数量({n_clusters})，将聚类数量调整为 {len(valid_stocks)}"
            )
            n_clusters = len(valid_stocks)

        # 准备不等长时间序列列表
        time_series = []
        for code in valid_stocks:
            # 提取累计收益率并转换为numpy数组
            ts = stock_dfs[code]['cumulative_return'].values
            time_series.append(ts)

        # 创建并训练模型
        logger.info("使用DTW距离度量进行聚类，这可能需要一些时间...")
        km = TimeSeriesKMeans(n_clusters=n_clusters, metric="dtw", random_state=0, n_jobs=-1)
        time_series = to_time_series_dataset(time_series)
        y_pred = km.fit_predict(time_series)

        # 将结果整理为聚类簇到股票列表的映射
        clusters = {i: [] for i in range(n_clusters)}
        for i, code in enumerate(valid_stocks):
            cluster_id = y_pred[i]
            clusters[cluster_id].append(code)

        # 计算每个聚类的大小
        cluster_sizes = {i: len(stocks) for i, stocks in clusters.items()}
        logger.info(f"聚类完成，各聚类大小: {cluster_sizes}")

        return clusters, km.cluster_centers_

    except Exception as e:
        logger.error(f"聚类股票时出错: {str(e)}")
        return {}, np.array([])


def plot_clusters(
    stock_dfs: Dict[str, pd.DataFrame],
    clusters: Dict[int, List[str]],
    barycenters: np.ndarray,
    output_dir: str = "./output",
):
    """
    为每个聚类绘制累计收益率曲线和中心点
    处理不等长时间序列的情况

    Args:
        stock_dfs: 股票代码到DataFrame的映射
        clusters: 聚类簇到股票代码列表的映射
        barycenters: 聚类中心数组
        output_dir: 输出图像的目录
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 获取基准指数的数据
        benchmark_df = stock_dfs['000001.SH']

        # 为每个聚类绘制图表
        n_clusters = len(clusters)

        # 绘制所有聚类的总览图（使用基准指数的时间轴）
        plt.figure(figsize=(15, 10))

        # 绘制基准指数
        plt.plot(
            benchmark_df.index,
            benchmark_df['cumulative_return'],
            color='black',
            linewidth=2,
            label='上证指数',
        )

        # 为每个聚类绘制中心点(这里只是示意性绘制，因为中心点可能没有明确的时间对应关系)
        colors = plt.cm.rainbow(np.linspace(0, 1, n_clusters))
        # 创建一个通用的x轴，对应每个中心点的长度
        for i in range(n_clusters):
            # 对于每个中心点，创建一个等长的时间序列(仅用于绘图)
            x_points = np.linspace(0, 1, len(barycenters[i]))
            scaled_x = np.interp(x_points, [0, 1], [0, len(benchmark_df) - 1])
            # 将x坐标映射到实际日期
            date_indices = [int(x) for x in scaled_x]
            plot_dates = [benchmark_df.index[idx] for idx in date_indices]

            plt.plot(
                plot_dates,
                barycenters[i].ravel(),
                color=colors[i],
                linewidth=2,
                label=f'聚类 {i + 1} (中心点, {len(clusters[i])}支股票)',
            )

        plt.title('各聚类累计收益率中心点与上证指数对比')
        plt.xlabel('日期')
        plt.ylabel('累计收益率')
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.savefig(f"{output_dir}/all_clusters_barycenters.png", dpi=300, bbox_inches='tight')

        # 为每个聚类单独绘制详细图表
        for i in range(n_clusters):
            plt.figure(figsize=(15, 8))

            # 绘制基准指数
            plt.plot(
                benchmark_df.index,
                benchmark_df['cumulative_return'],
                color='black',
                linewidth=2,
                label='上证指数',
            )

            # 绘制该聚类的所有股票
            for code in clusters[i]:
                stock_df = stock_dfs[code]
                plt.plot(
                    stock_df.index,  # 使用股票自己的日期索引
                    stock_df['cumulative_return'],
                    color='lightgray',
                    linewidth=1,
                    alpha=0.5,
                )

            # 绘制中心点(根据基准指数的时间轴进行缩放)
            # 对于每个中心点，创建一个等长的时间序列(仅用于绘图)
            x_points = np.linspace(0, 1, len(barycenters[i]))
            scaled_x = np.interp(x_points, [0, 1], [0, len(benchmark_df) - 1])
            # 将x坐标映射到实际日期
            date_indices = [int(x) for x in scaled_x]
            plot_dates = [benchmark_df.index[idx] for idx in date_indices]

            plt.plot(
                plot_dates,
                barycenters[i].ravel(),
                color=colors[i],
                linewidth=3,
                label=f'聚类 {i + 1} 中心点',
            )

            # 绘制该聚类中前5支股票（为图例）
            top_stocks = clusters[i][:5]
            for code in top_stocks:
                stock_df = stock_dfs[code]
                plt.plot(
                    stock_df.index,  # 使用股票自己的日期索引
                    stock_df['cumulative_return'],
                    linewidth=1.5,
                    label=code,
                )

            plt.title(f'聚类 {i + 1} 累计收益率曲线 (共{len(clusters[i])}支股票)')
            plt.xlabel('日期')
            plt.ylabel('累计收益率')
            plt.legend()
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.savefig(f"{output_dir}/cluster_{i + 1}_details.png", dpi=300, bbox_inches='tight')

        logger.info(f"图表已保存到 {output_dir} 目录")

    except Exception as e:
        logger.error(f"绘制聚类图表时出错: {str(e)}")
        logger.error(f"错误详情: {e}", exc_info=True)


def main():
    """
    主函数：获取沪深A股股票列表并进行时间序列聚类分析
    """
    try:
        quantback.setup_logging()

        # 获取沪深A股股票列表
        stock_list = get_stock_list()
        if not stock_list:
            logger.error("无法获取沪深A股股票列表")
            return

        logger.info(f"获取到 {len(stock_list)} 只沪深A股")

        # 使用一部分股票进行测试
        test_stocks = stock_list[:1000]  # 使用前100只股票进行测试
        logger.info(f"使用 {len(test_stocks)} 只股票进行测试")

        # 设置开始和结束日期
        start_time = "2024-01-01"  # 可以根据需要修改
        end_time = "2024-12-31"  # 可以根据需要修改

        # 预处理数据
        processed_dfs = preprocess_stock_data(
            test_stocks, benchmark_code='000001.SH', start_time=start_time, end_time=end_time
        )

        # 展示数据样例
        if processed_dfs and '000001.SH' in processed_dfs:
            benchmark_df = processed_dfs['000001.SH']
            logger.info(
                f"上证指数数据范围: {benchmark_df.index.min()} 至 {benchmark_df.index.max()}"
            )

            if len(processed_dfs) > 1:
                # 获取第一只股票作为样例
                sample_stock = next(
                    (code for code in processed_dfs.keys() if code != '000001.SH'), None
                )
                if sample_stock:
                    sample_df = processed_dfs[sample_stock]
                    logger.info(
                        f"样例股票 {sample_stock} 数据范围: {sample_df.index.min()} 至 {sample_df.index.max()}"
                    )
                    logger.info(f"数据示例:\n{sample_df.head()}")

        # 执行聚类
        n_clusters = 15  # 聚类数量
        clusters, barycenters = cluster_stocks(processed_dfs, n_clusters)

        # 创建输出目录
        output_dir = os.path.join("output")
        os.makedirs(output_dir, exist_ok=True)

        # 绘制聚类结果
        plot_clusters(processed_dfs, clusters, barycenters, output_dir)

    except Exception as e:
        logger.error(f"执行主函数时出错: {str(e)}")
        logger.error("错误详情:", exc_info=True)


if __name__ == "__main__":
    main()
