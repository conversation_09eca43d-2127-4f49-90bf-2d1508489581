import { createRouter, createWebHistory } from 'vue-router'
import BacktestView from '../views/BacktestView.vue'
import HelloWorldView from '../views/HelloWorldView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'backtest',
      component: BacktestView,
    },
    {
      path: '/backtest',
      name: 'backtest-alt',
      component: BacktestView,
    },
    {
      path: '/hello',
      name: 'hello',
      component: HelloWorldView,
    },
  ],
})

export default router

