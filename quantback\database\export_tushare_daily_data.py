#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从tushare下载每日股票数据的脚本
- 从1990年12月19日开始下载
- 每日数据生成一个CSV文件
- 每年数据打一个ZIP压缩包
- 只传trade_date参数，不传ts_code
"""

import time
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

import pandas as pd
import tushare as ts
from tqdm import tqdm
from wakepy import keep

import quantback


def download_daily_data(pro, trade_date: str, max_retries: int = 3) -> Optional[pd.DataFrame]:
    """下载指定日期的每日数据，支持重试"""
    for attempt in range(max_retries):
        try:
            # 调用tushare daily接口，只传trade_date参数
            df = pro.daily(trade_date=trade_date)

            if df is None or df.empty:
                if attempt == 0:  # 只在第一次尝试时打印无数据信息
                    tqdm.write(f"  {trade_date}: 无数据")
                return None

            tqdm.write(f"  {trade_date}: 获取到 {len(df)} 条记录")
            return df

        except Exception as e:
            if attempt < max_retries - 1:
                tqdm.write(
                    f"  {trade_date}: 下载失败，重试中... ({attempt + 1}/{max_retries}) - {e}"
                )
                time.sleep(1)  # 等待1秒后重试
            else:
                tqdm.write(f"  {trade_date}: 下载失败 - {e}")
                raise RuntimeError(f"下载数据失败，程序终止: {trade_date} - {e}")

    return None


def save_daily_csv(df: pd.DataFrame, trade_date: str, output_dir: Path) -> str:
    """保存每日数据为CSV文件"""
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)

    # 生成文件名
    filename = f"{trade_date}.csv"
    filepath = output_dir / filename

    # 保存CSV文件
    df.to_csv(filepath, index=False, encoding='utf-8')

    return str(filepath)


def add_to_yearly_zip(csv_file: str, year: int, output_dir: Path):
    """将CSV文件添加到年度ZIP压缩包"""
    zip_filename = f"daily_{year}.zip"
    zip_filepath = output_dir / zip_filename

    # 如果ZIP文件已存在，使用追加模式
    mode = 'a' if zip_filepath.exists() else 'w'

    csv_path = Path(csv_file)
    if csv_path.exists():
        with zipfile.ZipFile(zip_filepath, mode, zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(csv_file, csv_path.name)
        # 删除原CSV文件
        csv_path.unlink()
        tqdm.write(f"  已添加到 {zip_filename}")
    else:
        tqdm.write(f"  CSV文件不存在: {csv_file}")


def main():
    """主函数"""
    # 设置参数
    START_DATE = "19901219"  # A股最早上市日期
    END_DATE = datetime.now().strftime("%Y%m%d")  # 当前日期
    OUTPUT_DIR = Path("stock_data")  # 输出目录

    print("=" * 60)
    print("从tushare下载每日股票数据")
    print(f"开始日期: {START_DATE}")
    print(f"结束日期: {END_DATE}")
    print(f"输出目录: {OUTPUT_DIR}")
    print("=" * 60)

    try:
        # 初始化tushare pro api对象
        config = quantback.get_config()
        token = config.get('tushare.token')
        ts.set_token(token)
        pro = ts.pro_api()
        # 生成日期范围（按自然日）
        start_dt = datetime.strptime(START_DATE, "%Y%m%d")
        end_dt = datetime.strptime(END_DATE, "%Y%m%d")

        total_days = (end_dt - start_dt).days + 1
        print(f"从 {START_DATE} 到 {END_DATE}，共 {total_days} 个自然日")

        # 遍历每个自然日
        current_dt = start_dt
        success_count = 0

        with tqdm(total=total_days, desc="下载数据") as pbar:
            while current_dt <= end_dt:
                trade_date = current_dt.strftime("%Y%m%d")
                year = current_dt.year

                # 下载数据
                df = download_daily_data(pro, trade_date)
                if df is not None:
                    # 保存CSV文件
                    csv_file = save_daily_csv(df, trade_date, OUTPUT_DIR)

                    # 立即添加到年度ZIP包
                    add_to_yearly_zip(csv_file, year, OUTPUT_DIR)
                    success_count += 1

                # 每下载100个文件后暂停一下，避免请求过于频繁
                if success_count % 100 == 0 and success_count > 0:
                    time.sleep(0.5)

                # 移动到下一天
                current_dt += timedelta(days=1)
                pbar.update(1)

        print(f"\n数据下载完成！成功下载 {success_count} 个交易日的数据")
        print(f"输出目录: {OUTPUT_DIR.absolute()}")

    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
