import atexit
import json
import logging
import os
import threading
import time
from pathlib import Path
from typing import Dict

from xtquant import xtconstant, xtdata
from xtquant import xtpythonclient as _XTQC_
from xtquant import xttype as _XTTYPE_
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount

import quantback
from quantback.utils import SingletonMeta

logger = logging.getLogger(__name__)


class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def __init__(self, xtService):
        self.xtService = xtService

    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        logger.info("connection lost")

    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        logger.info("on_stock_order")
        logger.info(
            f"stock_code:{order.stock_code} order_status:{order.order_status} order_sysid:{order.order_sysid}"
        )

    def on_stock_asset(self, asset):
        """
        资金变动推送
        :param asset: XtAsset对象
        :return:
        """
        logger.info("on_stock_asset")
        logger.info(f"账号:{asset.account_id} cash:{asset.cash} total_asset:{asset.total_asset}")

    def on_stock_trade(self, trade):
        """
        成交变动推送
        :param trade: XtTrade对象
        :return:
        """
        logger.info("on_stock_trade")
        logger.info(
            f"账号:{trade.account_id} stock_code:{trade.stock_code} traded_volume:{trade.traded_volume}"
        )

        # 更新策略持仓
        strategy_name = trade.strategy_name or '未指定'
        if strategy_name not in self.xtService.strategy_positions:
            self.xtService.strategy_positions[strategy_name] = {}
        positions = self.xtService.strategy_positions[strategy_name]
        stock_code = trade.stock_code
        if stock_code not in positions:
            positions[stock_code] = 0

        # 根据买卖方向更新持仓
        volume_change = 0
        if trade.order_type == xtconstant.STOCK_BUY:
            positions[stock_code] += trade.traded_volume
            volume_change = trade.traded_volume
        elif trade.order_type == xtconstant.STOCK_SELL:
            positions[stock_code] -= trade.traded_volume
            volume_change = -trade.traded_volume
            # 如果持仓为0，删除该条记录.
            if positions[stock_code] == 0:
                del positions[stock_code]
                if not positions:  # 如果策略没有任何持仓，删除策略记录
                    del self.xtService.strategy_positions[strategy_name]

        # 记录交易到wal log
        self.xtService._append_to_wal_log(strategy_name, stock_code, volume_change)

    def on_stock_position(self, position):
        """
        持仓变动推送
        :param position: XtPosition对象
        :return:
        """
        logger.info("on_stock_position")
        logger.info(f"stock_code:{position.stock_code} volume:{position.volume}")

    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        logger.info("on_order_error")
        logger.info(
            f"order_id:{order_error.order_id} error_id:{order_error.error_id} error_msg:{order_error.error_msg}"
        )

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        logger.info("on_cancel_error")
        logger.info(
            f"order_id:{cancel_error.order_id} error_id:{cancel_error.error_id} error_msg:{cancel_error.error_msg}"
        )

    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        logger.info("on_order_stock_async_response")
        logger.info(f"账号:{response.account_id} order_id:{response.order_id} seq:{response.seq}")

    def on_cancel_order_stock_async_response(self, response):
        """
        异步撤单回报推送
        :param response: XtCancelOrderResponse 对象
        :return:
        """
        logger.info("on_cancel_order_stock_async_response")
        logger.info(f"账号:{response.account_id} order_id:{response.order_id} seq:{response.seq}")

    def on_account_status(self, status):
        """
        :param response: XtAccountStatus 对象
        :return:
        """
        logger.info("账号状态发生变化")
        logger.info(f"账号:{status.account_id} 类型:{status.account_type} 状态:{status.status}")


class MyXtQuantTrader(XtQuantTrader):
    """
    去除asyncio的代码。如果xtquant升级了，将下面代码与新父类对比一下。
    """

    def __init__(self, path, session, callback=None):
        """
        :param path: mini版迅投极速交易客户端安装路径下，userdata文件夹具体路径
        :param session: 当前任务执行所属的会话id
        :param callback: 回调方法
        """

        self.async_client = _XTQC_.XtQuantAsyncClient(path.encode('gb18030'), 'xtquant', session)
        self.callback = callback

        self.connected = False

        self.cbs = {}

        self.executor = None
        self.resp_executor = None

        self.relaxed_resp_order_enabled = False
        self.relaxed_resp_executor = None

        self.queuing_order_seq = set()  # 发起委托的seq,获取resp时移除
        self.handled_async_order_stock_order_id = set()  # 已处理了返回的委托order_id
        # 队列中的委托失败信息，在对应委托尚未返回(检测seq或者order_id)时存入，等待回调error_callback
        self.queuing_order_errors_byseq = {}
        self.queuing_order_errors_byid = {}

        self.handled_async_cancel_order_stock_order_id = set()
        self.handled_async_cancel_order_stock_order_sys_id = set()
        self.queuing_cancel_errors_by_order_id = {}
        self.queuing_cancel_errors_by_order_sys_id = {}

        #########################
        # push
        def on_common_push_callback_wrapper(argc, callback):
            if argc == 0:

                def on_push_data():
                    self.executor.submit(callback)

                return on_push_data
            elif argc == 1:

                def on_push_data(data):
                    self.executor.submit(callback, data)

                return on_push_data
            elif argc == 2:

                def on_push_data(data1, data2):
                    self.executor.submit(callback, data1, data2)

                return on_push_data
            else:
                return None

        # response
        def on_common_resp_callback(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                self.resp_executor.submit(callback, resp)
            return

        self.async_client.bindOnSubscribeRespCallback(on_common_resp_callback)
        self.async_client.bindOnUnsubscribeRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockAssetCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockOrdersCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockTradesCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockPositionsCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditDetailRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStkCompactsRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditSubjectsRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditSloCodeRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditAssureRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryNewPurchaseLimitCallback(on_common_resp_callback)
        self.async_client.bindOnQueryIPODataCallback(on_common_resp_callback)
        self.async_client.bindOnTransferRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryComFundRespCallback(on_common_resp_callback)
        self.async_client.bindOnSmtQueryQuoterRespCallback(on_common_resp_callback)
        self.async_client.bindOnSmtQueryOrderRespCallback(on_common_resp_callback)
        self.async_client.bindOnSmtQueryCompactRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryPositionStatisticsRespCallback(on_common_resp_callback)
        self.async_client.bindOnExportDataRespCallback(on_common_resp_callback)
        self.async_client.bindOnSyncTransactionFromExternalRespCallback(on_common_resp_callback)

        self.async_client.bindOnQueryAccountInfosCallback(on_common_resp_callback)
        self.async_client.bindOnQueryAccountStatusCallback(on_common_resp_callback)
        #########################

        enable_push = 1

        # order push

        def on_push_OrderStockAsyncResponse(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                resp = _XTTYPE_.XtOrderResponse(
                    resp.m_strAccountID,
                    resp.m_nOrderID,
                    resp.m_strStrategyName,
                    resp.m_strOrderRemark,
                    resp.m_strErrorMsg,
                    seq,
                )
                callback(resp)
                self.queuing_order_seq.discard(seq)
                e = self.queuing_order_errors_byseq.pop(seq, None)
                if not e:
                    e = self.queuing_order_errors_byid.pop(resp.order_id, None)
                if e is not None:
                    self.callback.on_order_error(e)
                else:
                    self.handled_async_order_stock_order_id.add(resp.order_id)
            return

        if enable_push:
            self.async_client.bindOnOrderStockRespCallback(
                on_common_push_callback_wrapper(2, on_push_OrderStockAsyncResponse)
            )

        def on_push_CancelOrderStockAsyncResponse(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                resp = _XTTYPE_.XtCancelOrderResponse(
                    resp.m_strAccountID,
                    resp.m_nCancelResult,
                    resp.m_nOrderID,
                    resp.m_strOrderSysID,
                    seq,
                    resp.m_strErrorMsg,
                )
                callback(resp)

                if not resp.order_sysid:
                    e = self.queuing_cancel_errors_by_order_id.pop(resp.order_id, None)
                    if e is not None:
                        self.handled_async_cancel_order_stock_order_id.discard(resp.order_id)
                        self.callback.on_cancel_error(e)
                    else:
                        self.handled_async_cancel_order_stock_order_id.add(resp.order_id)
                else:
                    e = self.queuing_cancel_errors_by_order_sys_id.pop(resp.order_sysid, None)
                    if e is not None:
                        self.handled_async_cancel_order_stock_order_sys_id.discard(resp.order_sysid)
                        self.callback.on_cancel_error(e)
                    else:
                        self.handled_async_cancel_order_stock_order_sys_id.add(resp.order_sysid)
            return

        if enable_push:
            self.async_client.bindOnCancelOrderStockRespCallback(
                on_common_push_callback_wrapper(2, on_push_CancelOrderStockAsyncResponse)
            )

        def on_push_disconnected():
            if self.callback:
                self.callback.on_disconnected()

        if enable_push:
            self.async_client.bindOnDisconnectedCallback(
                on_common_push_callback_wrapper(0, on_push_disconnected)
            )

        def on_push_AccountStatus(data):
            data = _XTTYPE_.XtAccountStatus(
                data.m_strAccountID, data.m_nAccountType, data.m_nStatus
            )
            self.callback.on_account_status(data)

        if enable_push:
            self.async_client.bindOnUpdateAccountStatusCallback(
                on_common_push_callback_wrapper(1, on_push_AccountStatus)
            )

        def on_push_StockAsset(data):
            self.callback.on_stock_asset(data)

        if enable_push:
            self.async_client.bindOnStockAssetCallback(
                on_common_push_callback_wrapper(1, on_push_StockAsset)
            )

        def on_push_OrderStock(data):
            self.callback.on_stock_order(data)

        if enable_push:
            self.async_client.bindOnStockOrderCallback(
                on_common_push_callback_wrapper(1, on_push_OrderStock)
            )

        def on_push_StockTrade(data):
            self.callback.on_stock_trade(data)

        if enable_push:
            self.async_client.bindOnStockTradeCallback(
                on_common_push_callback_wrapper(1, on_push_StockTrade)
            )

        def on_push_StockPosition(data):
            self.callback.on_stock_position(data)

        if enable_push:
            self.async_client.bindOnStockPositionCallback(
                on_common_push_callback_wrapper(1, on_push_StockPosition)
            )

        def on_push_OrderError(data):
            if (
                data.seq not in self.queuing_order_seq
                or data.order_id in self.handled_async_order_stock_order_id
            ):
                self.handled_async_order_stock_order_id.discard(data.order_id)
                self.callback.on_order_error(data)
            else:
                self.queuing_order_errors_byseq[data.seq] = data
                self.queuing_order_errors_byid[data.order_id] = data

        if enable_push:
            self.async_client.bindOnOrderErrorCallback(
                on_common_push_callback_wrapper(1, on_push_OrderError)
            )

        def on_push_CancelError(data):
            if data.order_id in self.handled_async_cancel_order_stock_order_id:
                self.handled_async_cancel_order_stock_order_id.discard(data.order_id)
                self.callback.on_cancel_error(data)
            elif data.order_sysid in self.handled_async_cancel_order_stock_order_sys_id:
                self.handled_async_cancel_order_stock_order_sys_id.discard(data.order_sysid)
                self.callback.on_cancel_error(data)
            else:
                self.queuing_cancel_errors_by_order_id[data.order_id] = data
                self.queuing_cancel_errors_by_order_sys_id[data.order_sysid] = data

        if enable_push:
            self.async_client.bindOnCancelErrorCallback(
                on_common_push_callback_wrapper(1, on_push_CancelError)
            )

        def on_push_SmtAppointmentAsyncResponse(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                resp = _XTTYPE_.XtSmtAppointmentResponse(
                    seq, resp.m_bSuccess, resp.m_strMsg, resp.m_strApplyID
                )
                callback(resp)
            return

        if enable_push:
            self.async_client.bindOnSmtAppointmentRespCallback(
                on_common_push_callback_wrapper(2, on_push_SmtAppointmentAsyncResponse)
            )

        # 判断并存储运行模式
        self.is_test_mode = os.getenv('MODE') == 'test'

    def order_stock_async(
        self,
        account,
        stock_code,
        order_type,
        order_volume,
        price_type,
        price,
        strategy_name='',
        order_remark='',
    ):
        # 将来如果短时间下很多单, 需要使用utils.py的rate_limit装饰器限流.
        logger.info(
            f"下单参数: 账户id={account.account_id}, 股票代码={stock_code}, 订单类型={order_type}, 数量={order_volume}, 价格类型={price_type}, 价格={price}, 策略名称={strategy_name}, 备注={order_remark}"
        )
        if self.is_test_mode:
            logger.info("模拟下单")
            seq = int(time.time_ns())  # 生成一个模拟的订单序列号
        else:
            seq = super().order_stock_async(
                account,
                stock_code,
                order_type,
                order_volume,
                price_type,
                price,
                strategy_name,
                order_remark,
            )
        return seq

    def stop(self):
        self.async_client.stop()
        self.executor.shutdown(wait=False)
        self.relaxed_resp_executor.shutdown(wait=False)

    def __del__(self):
        pass


class TradeService(metaclass=SingletonMeta):
    def __init__(self):
        self.xt_trader = None
        config = quantback.get_config()
        self.miniqmt_dir = config.get('miniqmt.dir')
        logger.debug(f"使用配置的路径: {self.miniqmt_dir}")

        self.active_profile = str(config.get('miniqmt.active_profile'))
        logger.debug(f"当前激活的配置文件: {self.active_profile}")

        self.account_id = config.get(f'miniqmt.profiles.{self.active_profile}.account')
        self.account_type = config.get(f'miniqmt.profiles.{self.active_profile}.type')
        logger.debug(f"账户id: {self.account_id}, 类型: {self.account_type}")

        self.stock_account = StockAccount(self.account_id, account_type=self.account_type)
        self.callback = MyXtQuantTraderCallback(self)
        self.connect()

        # 文件路径
        self.data_dir = Path(__file__).parent
        self.positions_file = self.data_dir / "strategy_positions.json"
        self.wal_log_file = self.data_dir / "strategy_positions_wal.log"

        # wal log锁
        self._wal_log_lock = threading.Lock()

        # 保持wal log文件始终打开以提升性能
        self._wal_log_fp = None
        self._open_wal_log()

        # 初始化策略持仓
        self.strategy_positions = {}
        self._load_strategy_positions()

        # 注册程序退出时的保存函数
        atexit.register(self._save_on_exit)

    def _open_wal_log(self):
        """打开wal log文件，保持句柄打开以提升写入性能"""
        try:
            if self._wal_log_fp is None:
                self._wal_log_fp = open(self.wal_log_file, 'a')
                logger.debug("已打开wal log文件")
        except Exception as e:
            logger.error(f"打开wal log文件失败: {str(e)}")
            self._wal_log_fp = None

    def _close_wal_log(self):
        """关闭wal log文件句柄"""
        try:
            if self._wal_log_fp is not None:
                self._wal_log_fp.close()
                self._wal_log_fp = None
                logger.debug("已关闭wal log文件")
        except Exception as e:
            logger.error(f"关闭wal log文件失败: {str(e)}")

    def _save_on_exit(self):
        """在程序退出时保存策略持仓数据并清空wal log"""
        logger.info("程序退出，执行最终保存并清空wal log...")

        # 确保关闭wal log文件句柄
        self._close_wal_log()

        # 保存策略持仓
        if self._save_to_file_now():
            # 成功保存后清空wal log
            self._clear_wal_log()
        else:
            logger.error("退出时保存策略持仓失败，保留wal log以便下次启动恢复")

    def connect(self):
        """连接到迅投交易接口"""
        # 关闭之前的xt_trader
        if self.xt_trader:
            self.xt_trader.stop()
        session_id = int(time.time())
        self.xt_trader = MyXtQuantTrader(self.miniqmt_dir, session_id)  # 重连时必须更新session_id
        self.xt_trader.set_relaxed_response_order_enabled(
            True
        )  # qmt的交易接口push和response各用一个线程。不是qmt行情接口的push。
        self.xt_trader.register_callback(self.callback)
        self.xt_trader.start()

        connect_result = self.xt_trader.connect()
        if connect_result == 0:
            logger.info("连接成功")
            subscribe_result = self.xt_trader.subscribe(self.stock_account)
            if subscribe_result == 0:
                logger.info("账户订阅成功")
            else:
                logger.error("账户订阅失败")
            return True
        else:
            logger.error(f"连接失败:{connect_result}")
            return False

    def disconnect(self):
        """断开连接并关闭资源"""
        # 关闭wal log文件
        self._close_wal_log()
        # 断开交易连接
        self.xt_trader.stop()

    def order(self, stock_code, volume, strategy_name, round_to_100=True, price=None):
        """
        下单
        清仓可以使用order_target(volume=0), 允许包含碎股数量.
        """
        if not price:
            tick = xtdata.get_full_tick([stock_code])[stock_code]
            price = tick["lastPrice"]
        price_type = xtconstant.FIX_PRICE

        if round_to_100:
            # 科创板下单数量要>=200, 否则数量改为0; 可交易200股以上的零散股, 不用取整到100的整数倍
            if stock_code.startswith('688'):
                if volume < 200:
                    logger.warning(f"科创板下单数量小于200: {volume}")
                    volume = 0
            elif volume % 100 != 0:
                logger.warning(f"下单数量不是100的整数倍: {volume}")
                volume = int(volume / 100) * 100
        if volume > 0:
            order_type = xtconstant.STOCK_BUY
            price = max(price + 0.01, price * 1.0015)
        elif volume < 0:
            order_type = xtconstant.STOCK_SELL
            price = min(price - 0.01, price * 0.9985)
        else:
            return 0
        volume = abs(volume)

        return self.xt_trader.order_stock_async(
            self.stock_account, stock_code, order_type, volume, price_type, price, strategy_name, ''
        )

    def order_target(self, stock_code, volume, strategy_name, price=None):
        """下单到目标持仓数量"""
        current_volume = self.strategy_positions.get(strategy_name, {}).get(stock_code, 0)
        logger.info(
            f"股票代码: {stock_code}, 当前持仓数量: {current_volume}, 目标持仓数量: {volume}"
        )
        round_to_100 = volume != 0
        return self.order(
            stock_code, volume - current_volume, strategy_name, round_to_100, price=price
        )

    def order_value(self, stock_code, value, strategy_name, price=None):
        """下单value市值, 如果value为负数, 则卖出"""
        if not price:
            tick = xtdata.get_full_tick([stock_code])[stock_code]
            price = tick["lastPrice"]
        volume = value / price
        return self.order(stock_code, volume, strategy_name, price=price)

    def order_target_value(self, stock_code, value, strategy_name, price=None):
        """下单到目标持仓市值"""
        if not price:
            tick = xtdata.get_full_tick([stock_code])[stock_code]
            price = tick["lastPrice"]
        volume = value / price
        return self.order_target(stock_code, volume, strategy_name, price=price)

    def _load_strategy_positions(self):
        """加载策略持仓数据，并处理wal log"""
        # 先加载主JSON文件
        if self.positions_file.exists():
            try:
                with open(self.positions_file, 'r') as f:
                    self.strategy_positions = json.load(f)
                logger.info(f"已从文件加载策略持仓记录: {self.positions_file}")
            except Exception as e:
                logger.error(f"加载策略持仓数据时出错: {str(e)}")
                self.strategy_positions = {}
        else:
            self.strategy_positions = {}  # {strategy_name: {stock_code: volume}}
            logger.info("策略持仓文件不存在，初始化空持仓")

        # 处理wal log
        if self.wal_log_file.exists():
            try:
                # 检查文件是否为空
                if os.path.getsize(self.wal_log_file) == 0:
                    logger.info("wal log为空")
                    return

                # 一次性读取所有行，减少IO操作
                with open(self.wal_log_file, 'r') as f:
                    lines = f.readlines()

                if lines:
                    logger.info(f"发现wal log，包含{len(lines)}条交易记录，开始恢复...")

                    # 应用wal log中的交易记录
                    for line in lines:
                        try:
                            if not line.strip():
                                continue

                            # 解析记录 (格式: timestamp|strategy|stock|volume_change)
                            fields = line.strip().split('|')
                            if len(fields) < 4:
                                logger.warning(f"无效的wal log记录: {line}")
                                continue

                            timestamp, strategy_name, stock_code, volume_change = fields
                            volume_change = int(volume_change)

                            logger.debug(
                                f"应用wal log记录: 策略={strategy_name}, 股票={stock_code}, 变动量={volume_change}, 时间={timestamp}"
                            )

                            # 更新策略持仓
                            if strategy_name not in self.strategy_positions:
                                self.strategy_positions[strategy_name] = {}

                            if stock_code not in self.strategy_positions[strategy_name]:
                                self.strategy_positions[strategy_name][stock_code] = 0

                            self.strategy_positions[strategy_name][stock_code] += volume_change

                            # 如果持仓为0，清理记录
                            if self.strategy_positions[strategy_name][stock_code] == 0:
                                del self.strategy_positions[strategy_name][stock_code]
                                if not self.strategy_positions[strategy_name]:
                                    del self.strategy_positions[strategy_name]

                        except Exception as e:
                            logger.error(f"处理wal log记录时出错: {line}, 错误: {str(e)}")

                    logger.info("wal log处理完成")

                    # 恢复完成后保存当前状态并清空wal log
                    if self._save_to_file_now():
                        self._clear_wal_log()
                    else:
                        logger.error("恢复后保存策略持仓失败，保留wal log")

            except Exception as e:
                logger.error(f"处理wal log时出错: {str(e)}")

    def _append_to_wal_log(self, strategy_name: str, stock_code: str, volume_change: int):
        """追加交易记录到wal log，采用高性能方式"""
        if volume_change == 0:
            return  # 忽略无变动的交易

        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        record = f"{timestamp}|{strategy_name}|{stock_code}|{volume_change}\n"

        # 只在写入文件时获取锁
        with self._wal_log_lock:
            try:
                # 如果文件句柄不存在则重新打开
                if self._wal_log_fp is None or self._wal_log_fp.closed:
                    self._open_wal_log()

                # 直接写入并立即刷新到磁盘
                if self._wal_log_fp is not None:
                    self._wal_log_fp.write(record)
                    self._wal_log_fp.flush()

            except Exception as e:
                logger.error(f"追加到wal log时出错: {str(e)}")
                self._close_wal_log()

    def _clear_wal_log(self):
        """清空wal log文件"""
        with self._wal_log_lock:
            try:
                # 关闭现有文件句柄
                self._close_wal_log()

                # 清空文件内容
                with open(self.wal_log_file, 'w') as f:
                    pass  # 写入空内容

                logger.info("已清空wal log")
            except Exception as e:
                logger.error(f"清空wal log时出错: {str(e)}")

    def _save_to_file_now(self):
        """立即保存策略持仓数据到文件"""
        start_time = time.time()
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.positions_file), exist_ok=True)

            # 先写入临时文件，再重命名，避免写入中断导致文件损坏
            temp_file = f"{self.positions_file}.temp"
            with open(temp_file, 'w') as f:
                json.dump(self.strategy_positions, f, indent=2, ensure_ascii=False)

            # 重命名临时文件
            os.replace(temp_file, self.positions_file)

            logger.info(f"保存策略持仓数据完成，耗时: {(time.time() - start_time) * 1000:.2f}ms")
            return True
        except Exception as e:
            logger.error(f"保存策略持仓数据时出错: {str(e)}")
            return False

    def verify_positions(self):
        """
        获取实际总持仓并验证与策略持仓的一致性
        """

        # 查询这些股票的实际持仓
        total_positions = {}
        positions = self.xt_trader.query_stock_positions(self.stock_account)
        for position in positions:
            if position.stock_code == '888880.SH':
                continue
            total_positions[position.stock_code] = position.volume

        # 验证持仓
        return self.verify_total_positions(total_positions)

    def verify_total_positions(self, total_positions: Dict[str, int]):
        """
        验证策略持仓总和与实际持仓是否一致
        :param total_positions: {stock_code: volume} 实际总持仓
        """
        # 计算所有策略持仓之和
        calculated_positions = {}
        for strategy_positions in self.strategy_positions.values():
            for stock_code, volume in strategy_positions.items():
                calculated_positions[stock_code] = calculated_positions.get(stock_code, 0) + volume

        # 比对差异
        differences = []
        for stock_code, volume in total_positions.items():
            calc_volume = calculated_positions.get(stock_code, 0)
            if calc_volume != volume:
                differences.append(f"股票{stock_code}: 实际持仓{volume}, 策略持仓和{calc_volume}")

        if differences:
            logger.warning("发现持仓差异:")
            for diff in differences:
                logger.warning(diff)
            return False
        logger.info("总策略持仓与实际持仓一致")
        return True


if __name__ == "__main__":
    pass
