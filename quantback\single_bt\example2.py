import pandas as pd

import quantback
from quantback.single_bt import Backtest
from quantback.single_bt.strategies import (
    MAStrategy,
)
from quantback.market_interface.data_mgr import query_kline_from_duckdb


def prepare_backtest_data(
    symbol: str, start_time: str, end_time: str, period: str = '1d'
) -> pd.DataFrame:
    """
    准备回测数据，包括：
    1. 从DolphinDB查询K线数据
    2. 重命名列名为Backtest要求的格式（首字母大写）
    3. 设置时间列为索引
    4. 调整列的顺序

    Args:
        symbol: 股票代码，例如 '000852.SH'
        start_time: 开始时间，格式 'YYYY-MM-DD[ HH:MM:SS]'
        end_time: 结束时间，格式 'YYYY-MM-DD[ HH:MM:SS]'
        period: 数据周期，默认为'1d'(日线)，也可以是'1m'(1分钟),'5m'(5分钟)等

    Returns:
        pd.DataFrame: 处理后的数据框，包含 Open, High, Low, Close, Volume 列
    """
    df = query_kline_from_duckdb(symbol, start_time, end_time, period=period)
    if df is None or df.empty:
        return pd.DataFrame()

    # 重命名列名，将小写列名转换为首字母大写，以符合Backtest的要求
    df = df.rename(
        columns={"open": "Open", "high": "High", "low": "Low", "close": "Close", "volume": "Volume"}
    )
    df.set_index('trade_dt', inplace=True, drop=True)
    df.index.name = None
    df = df[["Open", "High", "Low", "Close", "Volume"]]

    return df


# 如果 bt.optimize用method='grid',就会调用multiprocessing.
# 在Windows上，使用 multiprocessing 模块时，必须将创建和启动进程的代码
# 放在 if __name__ == '__main__': 语句块中(只让主进程执行这里)，以避免出现递归创建进程的问题。
# 如果 bt.optimize用method='sambo',实测不会出现重复执行__init__.py文件的问题.
if __name__ == "__main__":
    quantback.setup_logging()
    df = prepare_backtest_data('000852.SH', '2025-01-01', '2025-03-31', period='15m')
    bt = Backtest(df, MAStrategy, cash=1_000_000, commission=0.000336, trade_on_close=True)

    stats, heatmap = bt.optimize(
        n1=[5, 50],
        n2=[10, 100],
        constraint=lambda p: p.n1 < p.n2,
        maximize='Equity Final [$]',
        max_tries=200,
        random_state=0,
        method='sambo',
        return_heatmap=True,
    )

    # stats = bt.run()

    print(stats)
    print(stats._strategy)
    # print(heatmap)
    print(stats._trades)
    bt.plot(open_browser=True)
