<template>
  <div class="container mx-auto p-4">
    <div class="space-y-1">
      <!-- 错误提示 -->
      <div v-if="error" class="alert alert-error">
        <AlertCircle class="stroke-current shrink-0 h-6 w-6" />
        <span>{{ error }}</span>
      </div>

      <!-- 回测配置区域 -->
      <div class="card bg-base-100 border">
        <div class="card-body">
          <h2 class="card-title">回测配置</h2>

          <div class="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
            <!-- 策略选择 -->
            <div>
              <label class="label">
                <span class="font-semibold">选择策略</span>
                <button
                  :class="`btn btn-ghost btn-xs ${isLoadingStrategies ? 'loading' : ''}`"
                  @click="fetchStrategies"
                  :disabled="disabled || isLoadingStrategies"
                  title="刷新策略列表"
                >
                  <RefreshCw v-if="!isLoadingStrategies" :size="14" />
                </button>
              </label>
              <select class="select" v-model="selectedStrategy" :disabled="disabled">
                <option value="">请选择策略文件</option>
                <option v-for="strategy in strategies" :key="strategy" :value="strategy">
                  {{ strategy }}
                </option>
              </select>
            </div>

            <!-- 开始日期 -->
            <div>
              <label class="label">
                <span class="font-semibold">开始日期</span>
              </label>
              <DatePicker v-model="startDate" placeholder="选择开始日期" :disabled="disabled" />
            </div>

            <!-- 结束日期 -->
            <div>
              <label class="label">
                <span class="font-semibold">结束日期</span>
              </label>
              <DatePicker v-model="endDate" placeholder="选择结束日期" :disabled="disabled" />
            </div>

            <!-- 初始资金 -->
            <div>
              <label class="label">
                <span class="font-semibold">初始资金</span>
              </label>
              <input
                type="number"
                class="input"
                v-model.number="initialCash"
                placeholder="请输入初始资金"
                :disabled="disabled"
                min="0"
                step="1000"
              />
            </div>

            <!-- 回测期间显示 -->
            <div class="text-center">
              <div
                v-if="startDate && endDate"
                :class="`text-sm ${backtestDuration > 0 ? 'text-blue-600' : 'text-red-500'}`"
              >
                回测时长:
                {{ backtestDuration }}
                个自然日
              </div>
            </div>

            <!-- 开始回测按钮 -->
            <div class="flex flex-col">
              <button
                class="btn btn-primary btn-lg"
                @click="handleStartBacktest"
                :disabled="!canStart || backtestStatus.is_running"
              >
                <template v-if="!backtestStatus.is_running">开始回测</template>
                <template v-else> <span class="loading"></span>回测进行中... </template>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 回测状态 -->
      <BacktestStatus
        v-if="backtestStatus.is_running || backtestStatus.current_strategy"
        :status="backtestStatus"
      />

      <!-- 收益曲线图 -->
      <ReturnsChart
        v-if="backtestStatus.is_running || backtestStatus.results"
        :backtest-status="backtestStatus"
      />

      <!-- 结果展示 -->
      <ResultsDisplay v-if="backtestStatus.results" :results="backtestStatus.results" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useStorage } from '@vueuse/core'
import { RefreshCw, AlertCircle } from 'lucide-vue-next'
import { socket } from '../utils/socket'
import BacktestStatus from '../components/BacktestStatus.vue'
import ReturnsChart from '../components/ReturnsChart.vue'
import ResultsDisplay from '../components/ResultsDisplay.vue'
import DatePicker from '../components/DatePicker.vue'

// 内部管理回测状态
const backtestStatus = reactive({
  is_running: false,
  current_strategy: null,
  start_date: null,
  end_date: null,
  initial_cash: null,
  progress: 0,
  error_message: null,
  task_id: null,
  results: null,
  realtime_result: null,
})

// 状态管理
const selectedStrategy = ref('')
const startDate = useStorage('backtest_start_date', '')
const endDate = useStorage('backtest_end_date', '')
const initialCash = useStorage('backtest_initial_cash', 1000000)
const error = ref('')

// 策略相关状态
const strategies = ref([])
const isLoadingStrategies = ref(false)

// 计算属性
const canStart = computed(() => {
  if (
    !selectedStrategy.value ||
    !startDate.value ||
    !endDate.value ||
    !initialCash.value ||
    initialCash.value <= 0
  ) {
    return false
  }
  // 检查结束日期是否大于开始日期
  return new Date(endDate.value) >= new Date(startDate.value)
})
const disabled = computed(() => backtestStatus.is_running)

// 计算回测时长（允许负数）
const backtestDuration = computed(() => {
  if (!startDate.value || !endDate.value) {
    return 0
  }
  return Math.max(
    Math.ceil((new Date(endDate.value) - new Date(startDate.value)) / (1000 * 60 * 60 * 24)) + 1,
    0,
  )
})

// 获取策略列表的函数
const fetchStrategies = () => {
  isLoadingStrategies.value = true

  socket.emit('get_strategies', (response) => {
    if (response.strategies) {
      strategies.value = response.strategies
    } else if (response.error) {
      console.error('获取策略列表失败:', response.error)
    }
    isLoadingStrategies.value = false
  })
}

const handleStartBacktest = () => {
  socket.emit(
    'start_backtest',
    {
      strategy: selectedStrategy.value,
      start_date: startDate.value,
      end_date: endDate.value,
      initial_cash: initialCash.value,
    },
    (response) => {
      if (response.error) {
        console.error('启动回测失败:', response.error)
        error.value = response.error
      } else {
        console.log(response.message)
        const task_id = response.task_id
        console.log('任务ID:', task_id)
      }
    },
  )
}

// 回测状态事件处理函数
function onStatusUpdate(status) {
  Object.assign(backtestStatus, status)

  // 当回测完成时，清空实时结果（因为完整数据会通过 results 传递）
  if (status.results && !status.is_running) {
    backtestStatus.realtime_result = null
  }
}

onMounted(() => {
  // 获取策略列表
  fetchStrategies()

  // 注册回测相关事件监听器
  socket.on('status_update', onStatusUpdate)
})

onUnmounted(() => {
  // 移除事件监听器
  socket.off('status_update', onStatusUpdate)
})
</script>
