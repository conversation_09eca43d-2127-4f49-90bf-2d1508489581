{"name": "quantai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@vueuse/core": "^13.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "highcharts": "^12.3.0", "highcharts-vue": "^2.0.1", "lucide-vue-next": "^0.525.0", "pikaday": "^1.8.2", "pinia": "^3.0.3", "reka-ui": "^2.3.2", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/vite": "^4.1.11", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "daisyui": "^5.0.46", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "prettier": "3.5.3", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}}