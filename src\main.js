import './index.css'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import HighchartsVue from 'highcharts-vue'
import * as Highcharts from 'highcharts'
import 'highcharts/modules/stock' // 导入Stock模块

const app = createApp(App)
app.use(router)
app.use(HighchartsVue) // 注册<highcharts>标签
app.mount('#app')

Highcharts.setOptions({
  accessibility: {
    enabled: false,
  },
})
