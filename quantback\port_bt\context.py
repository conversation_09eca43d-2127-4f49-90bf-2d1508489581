"""
上下文对象和全局状态管理
"""

from datetime import datetime
from types import SimpleNamespace
from typing import Any, Dict, Optional


class Context:
    """回测上下文对象，类似聚宽的context"""

    def __init__(self, initial_cash: float = 1000000.0):
        # 基本信息
        self.current_dt: Optional[datetime] = None
        self.previous_date: Optional[datetime] = None

        # 投资组合信息 - 延迟导入避免循环依赖
        from .portfolio import Portfolio

        self.portfolio = Portfolio(initial_cash, context=self)

        # 基准
        self.benchmark: Optional[str] = None

        # 交易成本配置
        self.order_costs: Dict[str, Any] = {}


# 全局状态实例，类似聚宽的g对象，使用SimpleNamespace实现
g = SimpleNamespace()
