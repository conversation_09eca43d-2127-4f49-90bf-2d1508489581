<template>
  <input
    ref="dateInput"
    type="text"
    class="input pika-single"
    :placeholder="placeholder"
    :disabled="disabled"
    readonly
  />
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import Pikaday from 'pikaday'
import dayjs from 'dayjs'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '选择日期',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  format: {
    type: String,
    default: 'YYYY-MM-DD',
  },
  yearRange: {
    type: Array,
    default: () => [2000, new Date().getFullYear()],
  },
  maxDate: {
    type: Date,
    default: () => new Date(),
  },
  minDate: {
    type: Date,
    default: null,
  },
  firstDay: {
    type: Number,
    default: 1,
  },
})

// Emits
const emit = defineEmits(['update:modelValue'])

// Refs
const dateInput = ref(null)
let picker = null

// 初始化Pikaday
onMounted(() => {
  picker = new Pikaday({
    field: dateInput.value,
    format: props.format,
    yearRange: props.yearRange,
    maxDate: props.maxDate,
    minDate: props.minDate,
    firstDay: props.firstDay,
    toString(date, format) {
      return dayjs(date).format(format)
    },
    parse(dateString, format) {
      return dayjs(dateString, format).toDate()
    },
    onSelect: function (date) {
      const formattedDate = this.toString()
      emit('update:modelValue', formattedDate)
    },
  })
  // 如果有初始值，设置到picker
  if (props.modelValue) {
    picker.setDate(props.modelValue, true)
  }
})

// 清理
onUnmounted(() => {
  if (picker) {
    picker.destroy()
  }
})
</script>

<style>
/* Pikaday样式可以在这里自定义 */
</style>
