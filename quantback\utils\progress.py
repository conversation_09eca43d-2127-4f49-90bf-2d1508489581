import time


class TextProgress:
    """
    文本进度显示工具, 用于替代tqdm
    使用方法：
        for item in TextProgress(iterable):
            # do something
    """

    def __init__(self, iterable, desc="Progressing", show_time=False):
        self.iterable = iterable
        self.desc = desc
        self.show_time = show_time
        self.length = len(iterable) if hasattr(iterable, '__len__') else None
        self.start_time = None

    def _format_time(self, seconds):
        """格式化时间显示为 HH:MM:SS"""
        if seconds < 0:
            return "00:00:00"
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def __iter__(self):
        if self.show_time:
            self.start_time = time.time()

        for i, item in enumerate(self.iterable, 1):
            # 构建进度显示字符串
            if self.length:
                # 添加总数和百分比
                percentage = (i / self.length) * 100
                progress_base = f"{self.desc}: {i}/{self.length} ({percentage:.1f}%)"

                # 根据show_time参数决定是否显示时间信息
                if self.show_time:
                    current_time = time.time()
                    elapsed_time = current_time - self.start_time

                    # 估算剩余时间
                    # 因为print在yield之前，当前第i项还未处理完成
                    # 所以已完成的项目数是 i-1
                    completed_items = i - 1
                    if completed_items > 0:
                        avg_time_per_item = elapsed_time / completed_items
                        remaining_items = self.length - completed_items  # 包括正在处理的第i项
                        estimated_remaining = avg_time_per_item * remaining_items

                        time_info = f"已用时间: {self._format_time(elapsed_time)} | 预计剩余: {self._format_time(estimated_remaining)}"
                        progress_str = f"{progress_base} | {time_info}"
                    else:
                        # 第一项还没有完成时，无法估算剩余时间
                        progress_str = progress_base
                else:
                    # 不显示时间信息
                    progress_str = progress_base
            else:
                # 没有总长度时的显示
                if self.show_time:
                    current_time = time.time()
                    elapsed_time = current_time - self.start_time
                    progress_str = f"{self.desc}: {i} | 已用时间: {self._format_time(elapsed_time)}"
                else:
                    progress_str = f"{self.desc}: {i}"
            print(progress_str)

            yield item
