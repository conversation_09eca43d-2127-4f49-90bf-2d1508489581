# 导入所有工具函数和类，保持向后兼容性
from .patterns import SingletonMeta, SingletonMetaThreadSafe
from .progress import TextProgress
from .rate_limit import rate_limit
from .stock import convert_stock_code
from .text import identity, to_pinyin_abbr

# 导出所有公共接口
__all__ = [
    # 设计模式
    'SingletonMeta',
    'SingletonMetaThreadSafe',
    # 文本处理
    'identity',
    'to_pinyin_abbr',
    # 速率限制
    'rate_limit',
    # 股票工具
    'convert_stock_code',
    # 进度显示
    'TextProgress',
]
