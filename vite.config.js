import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  server: {
    watch: {
      //此处如果不ignore, 会造成vite首次启动加载很慢.
      //被发现变更的文件通过plugins判断是否需要hmr. /src路径下的.txt文件会触发整个页面刷新, 而其它目录的txt不会.
      ignored: ['**/quantback/**', '**/.venv/**', '**/.idea/**', '**/.vscode/**'],
    },
  },
  plugins: [tailwindcss(), vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
