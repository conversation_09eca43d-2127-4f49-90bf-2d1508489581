"""
事件驱动的股票量化回测框架
参照聚宽(JoinQuant)API设计，使用ClickHouse数据源
"""

from .api import *
from .context import Context, g
from .engine import BacktestEngine
from .portfolio import OrderCost, Portfolio
from .statistics import Statistics
from .visualization import Visualizer

__version__ = '1.0.0'
__all__ = [
    'BacktestEngine',
    'Context',
    'g',
    'OrderCost',
    'Portfolio',
    'Statistics',
    'Visualizer',
    'set_benchmark',
    'set_order_cost',
    'order',
    'order_value',
    'order_target',
    'order_target_value',
]
