{"cells": [{"cell_type": "code", "execution_count": 2, "id": "bbf39aec", "metadata": {}, "outputs": [], "source": ["from xtquant import xtdata\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 21, "id": "04ad2f3e", "metadata": {}, "outputs": [], "source": ["# 下载k线数据(包含复权因子)\n", "xtdata.download_history_data(\n", "    '002505.SZ', '1d', start_time='19900101', end_time='', incrementally=False\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b22067c8", "metadata": {}, "outputs": [], "source": ["# 测试获取行业列表\n", "xtdata.download_sector_data()\n", "sector_list = xtdata.get_sector_list()\n", "sector_list"]}, {"cell_type": "code", "execution_count": null, "id": "3e8b744f", "metadata": {}, "outputs": [], "source": ["# 测试获取行业股票列表\n", "stocks = xtdata.get_stock_list_in_sector('沪深A股')\n", "stocks[:10]"]}, {"cell_type": "code", "execution_count": 5, "id": "3e420eb0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>settelementPrice</th>\n", "      <th>openInterest</th>\n", "      <th>preClose</th>\n", "      <th>suspendFlag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>20160731</th>\n", "      <td>1469980799999</td>\n", "      <td>22.766810</td>\n", "      <td>24.710845</td>\n", "      <td>21.665190</td>\n", "      <td>22.356403</td>\n", "      <td>2516000</td>\n", "      <td>2.704641e+09</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>22.464405</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20160831</th>\n", "      <td>1472659199999</td>\n", "      <td>22.205200</td>\n", "      <td>27.799701</td>\n", "      <td>21.751592</td>\n", "      <td>24.581243</td>\n", "      <td>4505523</td>\n", "      <td>5.148780e+09</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>22.356403</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20160930</th>\n", "      <td>1475251199999</td>\n", "      <td>24.516442</td>\n", "      <td>24.797247</td>\n", "      <td>22.788411</td>\n", "      <td>24.127635</td>\n", "      <td>997721</td>\n", "      <td>1.107467e+09</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>24.581243</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20201130</th>\n", "      <td>1606751999999</td>\n", "      <td>23.100912</td>\n", "      <td>26.538149</td>\n", "      <td>18.592328</td>\n", "      <td>18.815525</td>\n", "      <td>5294119</td>\n", "      <td>5.124257e+09</td>\n", "      <td>0.0</td>\n", "      <td>15</td>\n", "      <td>24.127619</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20201231</th>\n", "      <td>1609430399999</td>\n", "      <td>18.703927</td>\n", "      <td>19.797593</td>\n", "      <td>15.445247</td>\n", "      <td>15.936281</td>\n", "      <td>2172141</td>\n", "      <td>1.715273e+09</td>\n", "      <td>0.0</td>\n", "      <td>15</td>\n", "      <td>18.815525</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   time       open       high        low      close   volume  \\\n", "20160731  1469980799999  22.766810  24.710845  21.665190  22.356403  2516000   \n", "20160831  1472659199999  22.205200  27.799701  21.751592  24.581243  4505523   \n", "20160930  1475251199999  24.516442  24.797247  22.788411  24.127635   997721   \n", "20201130  1606751999999  23.100912  26.538149  18.592328  18.815525  5294119   \n", "20201231  1609430399999  18.703927  19.797593  15.445247  15.936281  2172141   \n", "\n", "                amount  settelementPrice  openInterest   preClose  suspendFlag  \n", "20160731  2.704641e+09               0.0             0  22.464405            0  \n", "20160831  5.148780e+09               0.0             0  22.356403            0  \n", "20160930  1.107467e+09               0.0             0  24.581243            0  \n", "20201130  5.124257e+09               0.0            15  24.127619            0  \n", "20201231  1.715273e+09               0.0            15  18.815525            0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 测试获取K线数据-日线\n", "stock_code = '000029.SZ'\n", "result: dict = xtdata.get_market_data_ex(\n", "    [],\n", "    [stock_code],\n", "    period=\"1mon\",\n", "    start_time=\"20160401\",\n", "    end_time=\"20210101\",\n", "    count=5,\n", "    dividend_type='back_ratio',\n", "    fill_data=False,\n", ")\n", "df = result[stock_code]\n", "# print(\"index的数据类型:\", df.index.dtype)\n", "# print(\"列的数据类型:\\n\", df.dtypes)\n", "df"]}, {"cell_type": "code", "execution_count": 19, "id": "4a716f8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["index的数据类型: object\n", "列的数据类型: time                  int64\n", "open                float64\n", "high                float64\n", "low                 float64\n", "close               float64\n", "volume                int64\n", "amount              float64\n", "settelementPrice    float64\n", "openInterest          int64\n", "preClose            float64\n", "suspendFlag           int32\n", "dtype: object\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>settelementPrice</th>\n", "      <th>openInterest</th>\n", "      <th>preClose</th>\n", "      <th>suspendFlag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [time, open, high, low, close, volume, amount, settelementPrice, openInterest, preClose, suspendFlag]\n", "Index: []"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 测试获取K线数据-1分钟\n", "stock_code = '002013.SZ'\n", "result: dict = xtdata.get_market_data_ex(\n", "    [],\n", "    [stock_code],\n", "    period=\"1m\",\n", "    start_time=\"20250101\",\n", "    end_time=\"\",\n", "    count=-1,\n", "    dividend_type='front_ratio',\n", "    fill_data=False,\n", ")\n", "df = result[stock_code]\n", "print(\"index的数据类型:\", df.index.dtype)\n", "print(\"列的数据类型:\", df.dtypes)\n", "df"]}, {"cell_type": "code", "execution_count": 5, "id": "4191fd18", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>interest</th>\n", "      <th>stockBonus</th>\n", "      <th>stockGift</th>\n", "      <th>allotNum</th>\n", "      <th>allotPrice</th>\n", "      <th>gugai</th>\n", "      <th>dr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>20121203</th>\n", "      <td>2012-12-02 16:00:00</td>\n", "      <td>0.0000</td>\n", "      <td>-0.617780</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.381690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20190114</th>\n", "      <td>2019-01-13 16:00:00</td>\n", "      <td>0.0000</td>\n", "      <td>0.110681</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.110097</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20200914</th>\n", "      <td>2020-09-13 16:00:00</td>\n", "      <td>0.1520</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.033049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20210913</th>\n", "      <td>2021-09-12 16:00:00</td>\n", "      <td>0.0700</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.013496</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20240918</th>\n", "      <td>2024-09-17 16:00:00</td>\n", "      <td>0.0453</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.012463</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20250331</th>\n", "      <td>2025-03-30 16:00:00</td>\n", "      <td>0.0661</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.016009</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        time  interest  stockBonus  stockGift  allotNum  \\\n", "20121203 2012-12-02 16:00:00    0.0000   -0.617780        0.0       0.0   \n", "20190114 2019-01-13 16:00:00    0.0000    0.110681        0.0       0.0   \n", "20200914 2020-09-13 16:00:00    0.1520    0.000000        0.0       0.0   \n", "20210913 2021-09-12 16:00:00    0.0700    0.000000        0.0       0.0   \n", "20240918 2024-09-17 16:00:00    0.0453    0.000000        0.0       0.0   \n", "20250331 2025-03-30 16:00:00    0.0661    0.000000        0.0       0.0   \n", "\n", "          allotPrice  gugai        dr  \n", "20121203         0.0    0.0  0.381690  \n", "20190114         0.0    0.0  1.110097  \n", "20200914         0.0    0.0  1.033049  \n", "20210913         0.0    0.0  1.013496  \n", "20240918         0.0    0.0  1.012463  \n", "20250331         0.0    0.0  1.016009  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["stock_code = '159919.SZ'  # '600519.SH'\n", "df = xtdata.get_divid_factors(stock_code, start_time=\"19900101\", end_time=\"\")\n", "df[\"time\"] = pd.to_datetime(df[\"time\"], unit='ms')\n", "# df.set_index(\"time\", inplace=True)\n", "df"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}