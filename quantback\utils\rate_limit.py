import time
from collections import deque
from functools import wraps


def rate_limit(max_calls=300, time_window=1):
    """
    速率限制装饰器，限制函数在给定滑动时间窗口内的最大调用次数
    非线程安全, 因为lock影响性能.

    Args:
        max_calls: 时间窗口内允许的最大调用次数，默认300次
        time_window: 时间窗口大小（秒），默认1秒

    Returns:
        装饰器函数

    示例:
        >>> @rate_limit(max_calls=5, time_window=1)
        >>> def test_func():
        >>>     print("函数被调用")
    """
    # 使用队列存储调用时间戳
    call_timestamps = deque()
    # 将时间窗口转换为纳秒
    time_window_ns = time_window * 1_000_000_000

    def decorator(func):
        @wraps(func)  # 用于保留原函数的元信息,如__name__, __doc__, ...
        def wrapper(*args, **kwargs):
            # 获取纳秒级时间戳
            current_time_ns = time.time_ns()

            # 清理过期的时间戳（超过time_window秒的）
            while call_timestamps and current_time_ns - call_timestamps[0] > time_window_ns:
                call_timestamps.popleft()

            # 检查是否超过调用限制, 将来可改为休眠一会继续执行, 即time.sleep()或asyncio.sleep().
            if len(call_timestamps) >= max_calls:
                raise ValueError(
                    f"调用频率超限：函数 {func.__name__} 在 {time_window} 秒内最多允许调用 {max_calls} 次"
                )

            # 记录当前调用时间戳
            call_timestamps.append(current_time_ns)

            # 执行原函数
            return func(*args, **kwargs)

        return wrapper

    return decorator
