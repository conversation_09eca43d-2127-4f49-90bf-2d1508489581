<template>
  <div class="card bg-base-100 border">
    <div class="card-body">
      <h2 class="card-title">
        回测状态
        <div :class="`badge ${status.is_running ? 'badge-warning' : 'badge-success'}`">
          {{ status.is_running ? '运行中' : '已完成' }}
        </div>
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="stat">
          <div class="stat-title">当前策略</div>
          <div class="stat-value text-lg">{{ status.current_strategy || '无' }}</div>
        </div>

        <div class="stat">
          <div class="stat-title">回测期间</div>
          <div class="stat-value text-lg">
            {{
              status.start_date && status.end_date
                ? `${status.start_date} 至 ${status.end_date}`
                : '未设置'
            }}
          </div>
        </div>

        <div class="stat">
          <div class="stat-title">初始资金</div>
          <div class="stat-value text-lg">
            {{ status.initial_cash ? formatCurrency(status.initial_cash) : '未设置' }}
          </div>
        </div>

        <div class="stat">
          <div class="stat-title">进度</div>
          <div class="stat-value text-lg">{{ status.progress }}%</div>
        </div>
      </div>

      <div v-if="status.is_running" class="mt-4">
        <div class="flex justify-between text-sm mb-1">
          <span>回测进度</span>
          <span>{{ status.progress }}%</span>
        </div>
        <progress
          class="progress progress-primary w-full"
          :value="status.progress"
          max="100"
        ></progress>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  status: {
    type: Object,
    required: true,
  },
})

const formatCurrency = (value) => {
  if (value === null || value === undefined || isNaN(value)) return '未设置'
  return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`
}
</script>
