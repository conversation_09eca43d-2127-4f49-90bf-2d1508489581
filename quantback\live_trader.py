import asyncio
import json
import logging
import os
from asyncio import CancelledError
from typing import Any, Dict, Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from pydantic import BaseModel, Field, field_validator
from redis.asyncio import Redis
from wakepy import keep

import quantback
from quantback.live_strats.routine_jobs import order_reverse_repo
from quantback.market_interface.trade_service import TradeService
from quantback.utils import to_pinyin_abbr

# 配置日志
logger = logging.getLogger(__name__)

# 创建XtService实例
xtService = None
redis_client = None


# --- 请求体模型 ---
class OrderRequest(BaseModel):
    """下单请求的数据模型"""

    action: str = 'order'  # 操作类型
    stock_code: str  # 股票代码，如: 600036.SH
    volume: int = Field(
        ...,  # ... 表示必填字段
        ge=-1000000,  # 可以为负数，表示卖出
        le=1000000,  # 设置最大值为100万股
        description='交易数量',
    )
    strategy_name: Optional[str] = None  # 策略名称 (可选)

    @field_validator('stock_code')
    @classmethod
    def validate_stock_code(cls, v):
        if not (v.endswith('.SH') or v.endswith('.SZ')):
            raise ValueError('股票代码必须以.SH或.SZ结尾')
        return v


class OrderTargetRequest(BaseModel):
    """目标持仓请求的数据模型"""

    action: str = 'order_target'  # 操作类型
    stock_code: str  # 股票代码，如: 600036.SH
    volume: int = Field(
        ...,
        ge=0,  # 可以为0，表示清仓
        le=1000000,
        description='目标持仓数量',
    )
    strategy_name: Optional[str] = None  # 策略名称 (可选)

    @field_validator('stock_code')
    @classmethod
    def validate_stock_code(cls, v):
        if not (v.endswith('.SH') or v.endswith('.SZ')):
            raise ValueError('股票代码必须以.SH或.SZ结尾')
        return v


class OrderValueRequest(BaseModel):
    """下单市值请求的数据模型"""

    action: str = 'order_value'  # 操作类型
    stock_code: str  # 股票代码，如: 600036.SH
    value: float = Field(..., ge=-1000000, le=1000000, description='下单市值')
    strategy_name: Optional[str] = None  # 策略名称 (可选)

    @field_validator('stock_code')
    @classmethod
    def validate_stock_code(cls, v):
        if not (v.endswith('.SH') or v.endswith('.SZ')):
            raise ValueError('股票代码必须以.SH或.SZ结尾')
        return v


class OrderTargetValueRequest(BaseModel):
    """目标持仓市值请求的数据模型"""

    action: str = 'order_target_value'  # 操作类型
    stock_code: str  # 股票代码，如: 600036.SH
    value: float = Field(..., ge=0, le=1000000, description='目标持仓市值')
    strategy_name: Optional[str] = None  # 策略名称 (可选)

    @field_validator('stock_code')
    @classmethod
    def validate_stock_code(cls, v):
        if not (v.endswith('.SH') or v.endswith('.SZ')):
            raise ValueError('股票代码必须以.SH或.SZ结尾')
        return v


# 处理下单请求
async def handle_order(data: Dict[str, Any]) -> None:
    """处理下单请求"""
    try:
        # 验证请求数据
        request = OrderRequest(**data)

        # 执行下单
        seq = xtService.order(
            stock_code=request.stock_code,
            volume=request.volume,
            strategy_name=to_pinyin_abbr(request.strategy_name),
        )
        logger.info(f'下单序列号: {seq}')
    except ValueError as e:
        logger.error(f'下单请求参数错误: {str(e)}')
    except Exception as e:
        logger.error(f'下单失败: {str(e)}', exc_info=True)


# 处理目标持仓请求
async def handle_order_target(data: Dict[str, Any]) -> None:
    """处理目标持仓请求"""
    try:
        # 验证请求数据
        request = OrderTargetRequest(**data)

        # 执行目标持仓
        seq = xtService.order_target(
            stock_code=request.stock_code,
            volume=request.volume,
            strategy_name=to_pinyin_abbr(request.strategy_name),
        )
        logger.info(f'下单序列号: {seq}')
    except ValueError as e:
        logger.error(f'目标持仓请求参数错误: {str(e)}')
    except Exception as e:
        logger.error(f'目标持仓失败: {str(e)}', exc_info=True)


# 处理下单市值请求
async def handle_order_value(data: Dict[str, Any]) -> None:
    """处理下单市值请求"""
    try:
        # 验证请求数据
        request = OrderValueRequest(**data)

        # 执行下单市值
        seq = xtService.order_value(
            stock_code=request.stock_code,
            value=request.value,
            strategy_name=to_pinyin_abbr(request.strategy_name),
        )
        logger.info(f'下单序列号: {seq}')
    except ValueError as e:
        logger.error(f'下单市值请求参数错误: {str(e)}')
    except Exception as e:
        logger.error(f'下单市值失败: {str(e)}', exc_info=True)


# 处理目标持仓市值请求
async def handle_order_target_value(data: Dict[str, Any]) -> None:
    """处理目标持仓市值请求"""
    try:
        # 验证请求数据
        request = OrderTargetValueRequest(**data)

        # 执行目标持仓市值
        seq = xtService.order_target_value(
            stock_code=request.stock_code,
            value=request.value,
            strategy_name=to_pinyin_abbr(request.strategy_name),
        )
        logger.info(f'下单序列号: {seq}')
    except ValueError as e:
        logger.error(f'目标持仓市值请求参数错误: {str(e)}')
    except Exception as e:
        logger.error(f'目标持仓市值失败: {str(e)}', exc_info=True)


# 处理测试请求
async def handle_hello(data: Dict[str, Any]) -> None:
    """处理测试请求"""
    logger.info(f'收到测试请求: {data}')


# 根据操作类型路由到相应的处理函数
async def route_request(data: Dict[str, Any]) -> None:
    """根据操作类型路由到相应的处理函数"""
    action = data.get('action')
    logger.info(f'收到请求: {data}')
    handlers = {
        'order': handle_order,
        'order_target': handle_order_target,
        'order_value': handle_order_value,
        'order_target_value': handle_order_target_value,
        'hello': handle_hello,  # 添加测试处理器
    }

    handler = handlers.get(action)
    if not handler:
        logger.error(f'不支持的操作类型: {action}')
        return

    await handler(data)


# 处理Redis消息
async def process_message(message_data: str) -> None:
    """处理Redis消息"""
    try:
        # 解析JSON消息
        data = json.loads(message_data)

        # 路由请求
        await route_request(data)
    except json.JSONDecodeError:
        logger.error(f'无效的JSON格式: {message_data}')
    except Exception as e:
        logger.error(f'处理消息时发生错误: {str(e)}')


# 监听Redis通道
async def subscribe_channel(channel: str):
    """监听Redis通道"""
    global redis_client

    try:
        # 创建发布订阅对象
        pubsub = redis_client.pubsub()

        # 订阅命令通道
        await pubsub.subscribe(channel)

        logger.info(f'开始监听交易命令通道: {channel}')

        # 持续监听消息
        async for message in pubsub.listen():
            if message['type'] == 'message':
                try:
                    # 处理消息
                    await process_message(message['data'])
                except Exception as e:
                    logger.error(f'处理消息时发生异常: {str(e)}')
    except CancelledError:
        logger.info('Redis监听任务被取消')
    except Exception as e:
        logger.error(f'Redis订阅出现错误: {str(e)}')
    finally:
        # 取消订阅
        await pubsub.unsubscribe(channel)


# 初始化系统
async def initialize():
    """初始化系统"""
    global xtService, redis_client

    logger.info('正在连接到XTQuant...')
    xtService = TradeService()

    logger.info('正在连接到Redis...')
    redis_client = Redis(
        host='redis-15767.c290.ap-northeast-1-2.ec2.redns.redis-cloud.com',
        port=15767,
        db=0,
        username='default',
        password='ia7uT8gUQOUeZWIMextTOGBdo5kJHH4h',
        decode_responses=True,
    )

    # 验证Redis连接
    await redis_client.ping()
    logger.info('Redis连接成功')


# 关闭系统
async def shutdown():
    """关闭系统"""
    global xtService, redis_client

    logger.info('正在关闭XTQuant连接...')
    if xtService:
        xtService.disconnect()

    logger.info('正在关闭Redis连接...')
    if redis_client:
        await redis_client.aclose()

    # 取消所有任务
    tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
    if tasks:
        logger.info(f'正在取消 {len(tasks)} 个运行中的任务...')
        for task in tasks:
            task.cancel()
        await asyncio.gather(*tasks, return_exceptions=True)


async def tick_job():
    """
    定时任务，每秒执行一次, 用于验证apscheduler是否正常工作.
    """
    logger.info('Tick job executed')


# 主函数
async def main():
    """主函数"""
    # 交易命令通道
    global channel
    scheduler = None
    try:
        # 初始化系统
        await initialize()

        # 设置定时任务
        scheduler = AsyncIOScheduler()
        scheduler.start()
        # 对于coroutine, 会在本线程的event loop中执行; 对于function, 会在thread pool中执行
        scheduler.add_job(order_reverse_repo, 'cron', hour=14, minute=45, second=0)

        # 启动Redis监听
        await subscribe_channel(channel)
    except Exception as e:
        logger.error(f'系统运行出错: {str(e)}', exc_info=True)
    finally:
        await shutdown()
        if scheduler is not None:
            scheduler.shutdown()
            logger.info('定时任务已关闭')


if __name__ == '__main__':
    # 设置日志
    quantback.setup_logging()

    # 设置环境
    # os.environ['MODE'] = 'test'  # 测试环境
    channel = 'trade-test'
    print(f'当前环境: {os.getenv("MODE", "prod")}')

    # 运行程序
    with keep.running():  # 阻止系统休眠
        asyncio.run(main())
